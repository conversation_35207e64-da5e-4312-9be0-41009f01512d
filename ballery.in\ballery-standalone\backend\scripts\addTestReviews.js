/**
 * <PERSON><PERSON><PERSON> to add test reviews for Builder Ballery
 */

const mongoose = require('mongoose');
const Review = require('../models/Review');
require('dotenv').config();

const testReviews = [
    {
        customerName: '<PERSON><PERSON>',
        customerEmail: 'raj<PERSON>.<EMAIL>',
        customerPhone: '+919876543210',
        serviceName: 'Virtual Consultation',
        serviceType: 'virtual',
        rating: 5,
        title: 'Excellent Virtual Consultation',
        reviewText: 'Builder Ballery provided outstanding virtual consultation for my home renovation project. The team was professional, knowledgeable, and provided detailed insights that helped me make informed decisions. Highly recommended!',
        projectLocation: 'Mumbai, Maharashtra',
        projectType: 'Home Renovation',
        projectValue: 250000,
        status: 'approved',
        showOnWebsite: true,
        isFeatured: true
    },
    {
        customerName: '<PERSON><PERSON> Sharma',
        customerEmail: '<EMAIL>',
        customerPhone: '+919876543211',
        serviceName: 'Site Visit Consultation',
        serviceType: 'site-visit',
        rating: 4,
        title: 'Great Site Visit Experience',
        reviewText: 'The site visit was very thorough and professional. The consultant provided valuable suggestions for our office space design. The only minor issue was a slight delay in the scheduled time.',
        projectLocation: 'Delhi, NCR',
        projectType: 'Office Design',
        projectValue: 500000,
        status: 'approved',
        showOnWebsite: true
    },
    {
        customerName: 'Amit Patel',
        customerEmail: '<EMAIL>',
        serviceName: 'Virtual Consultation',
        serviceType: 'virtual',
        rating: 5,
        title: 'Outstanding Service Quality',
        reviewText: 'Exceptional service from start to finish. The virtual consultation was detailed and the team provided comprehensive solutions for our residential project. Will definitely use their services again.',
        projectLocation: 'Ahmedabad, Gujarat',
        projectType: 'Residential Construction',
        projectValue: 800000,
        status: 'approved',
        showOnWebsite: true,
        isFeatured: true
    },
    {
        customerName: 'Sneha Reddy',
        customerEmail: '<EMAIL>',
        serviceName: 'Site Visit Consultation',
        serviceType: 'site-visit',
        rating: 3,
        title: 'Average Experience',
        reviewText: 'The consultation was okay but I expected more detailed analysis. The consultant was knowledgeable but the recommendations could have been more specific to our requirements.',
        projectLocation: 'Hyderabad, Telangana',
        projectType: 'Commercial Space',
        projectValue: 300000,
        status: 'approved',
        showOnWebsite: true
    },
    {
        customerName: 'Vikram Singh',
        customerEmail: '<EMAIL>',
        serviceName: 'Virtual Consultation',
        serviceType: 'virtual',
        rating: 5,
        title: 'Highly Professional Team',
        reviewText: 'Builder Ballery exceeded my expectations with their virtual consultation service. The team was punctual, well-prepared, and provided innovative solutions for our villa project.',
        projectLocation: 'Jaipur, Rajasthan',
        projectType: 'Villa Construction',
        projectValue: 1200000,
        status: 'approved',
        showOnWebsite: true
    },
    {
        customerName: 'Kavya Nair',
        customerEmail: '<EMAIL>',
        serviceName: 'Site Visit Consultation',
        serviceType: 'site-visit',
        rating: 4,
        title: 'Good Value for Money',
        reviewText: 'The site visit consultation provided good value for the price. The consultant was experienced and gave practical suggestions for our apartment renovation.',
        projectLocation: 'Kochi, Kerala',
        projectType: 'Apartment Renovation',
        projectValue: 180000,
        status: 'approved',
        showOnWebsite: true
    },
    {
        customerName: 'Rohit Gupta',
        customerEmail: '<EMAIL>',
        serviceName: 'Virtual Consultation',
        serviceType: 'virtual',
        rating: 2,
        title: 'Below Expectations',
        reviewText: 'The virtual consultation did not meet our expectations. The session felt rushed and the recommendations were too generic. We were hoping for more personalized advice.',
        projectLocation: 'Pune, Maharashtra',
        projectType: 'Home Extension',
        projectValue: 150000,
        status: 'pending'
    },
    {
        customerName: 'Meera Joshi',
        customerEmail: '<EMAIL>',
        serviceName: 'Site Visit Consultation',
        serviceType: 'site-visit',
        rating: 5,
        title: 'Exceptional Site Analysis',
        reviewText: 'The site visit was incredibly detailed and professional. The consultant identified potential issues we hadn\'t considered and provided comprehensive solutions. Excellent service!',
        projectLocation: 'Bangalore, Karnataka',
        projectType: 'Independent House',
        projectValue: 950000,
        status: 'approved',
        showOnWebsite: true,
        isFeatured: true
    },
    {
        customerName: 'Arjun Malhotra',
        customerEmail: '<EMAIL>',
        serviceName: 'Virtual Consultation',
        serviceType: 'virtual',
        rating: 4,
        title: 'Satisfied with Service',
        reviewText: 'Good virtual consultation service. The team was knowledgeable and provided useful insights for our retail space design. Communication was clear and professional.',
        projectLocation: 'Chandigarh, Punjab',
        projectType: 'Retail Space',
        projectValue: 400000,
        status: 'approved',
        showOnWebsite: true
    },
    {
        customerName: 'Deepika Iyer',
        customerEmail: '<EMAIL>',
        serviceName: 'Site Visit Consultation',
        serviceType: 'site-visit',
        rating: 1,
        title: 'Very Disappointing',
        reviewText: 'Extremely disappointed with the service. The consultant arrived late, seemed unprepared, and provided very basic suggestions that we could have found online. Not worth the money.',
        projectLocation: 'Chennai, Tamil Nadu',
        projectType: 'Residential Plot',
        projectValue: 100000,
        status: 'rejected'
    }
];

async function addTestReviews() {
    try {
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/builder_ballery');
        console.log('✅ Connected to MongoDB');

        // Clear existing test reviews (optional)
        // await Review.deleteMany({ customerEmail: { $in: testReviews.map(r => r.customerEmail) } });

        // Add test reviews one by one to trigger pre-save middleware
        const reviews = [];
        for (const reviewData of testReviews) {
            const review = new Review(reviewData);
            await review.save();
            reviews.push(review);
        }
        console.log(`✅ Added ${reviews.length} test reviews`);

        // Display summary
        const stats = await Review.aggregate([
            { $group: { _id: '$status', count: { $sum: 1 } } }
        ]);
        
        console.log('\n📊 Review Statistics:');
        stats.forEach(stat => {
            console.log(`   ${stat._id}: ${stat.count}`);
        });

        console.log('\n🎉 Test reviews added successfully!');
        
    } catch (error) {
        console.error('❌ Error adding test reviews:', error);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Disconnected from MongoDB');
    }
}

// Run the script
if (require.main === module) {
    addTestReviews();
}

module.exports = { addTestReviews, testReviews };
