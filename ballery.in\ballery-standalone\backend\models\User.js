/**
 * BUILDER BALLERY - User Model
 * MongoDB schema for admin user management and authentication
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const userSchema = new mongoose.Schema({
    // Basic Information
    username: {
        type: String,
        required: true,
        unique: true,
        trim: true,
        minlength: 3,
        maxlength: 30,
        match: [/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores']
    },
    
    email: {
        type: String,
        required: true,
        unique: true,
        trim: true,
        lowercase: true,
        match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
    },
    
    password: {
        type: String,
        required: true,
        minlength: 8,
        select: false // Don't include password in queries by default
    },
    
    // Profile Information
    firstName: {
        type: String,
        trim: true,
        maxlength: 50
    },
    
    lastName: {
        type: String,
        trim: true,
        maxlength: 50
    },
    
    displayName: {
        type: String,
        trim: true,
        maxlength: 100
    },
    
    avatar: {
        filename: String,
        path: String,
        url: String
    },
    
    // Role and Permissions
    role: {
        type: String,
        enum: ['super_admin', 'admin', 'manager', 'staff'],
        default: 'admin',
        index: true
    },
    
    permissions: [{
        type: String,
        enum: [
            'bookings.view',
            'bookings.create',
            'bookings.edit',
            'bookings.delete',
            'inquiries.view',
            'inquiries.respond',
            'inquiries.delete',
            'services.view',
            'services.edit',
            'portfolio.view',
            'portfolio.edit',
            'reviews.view',
            'reviews.moderate',
            'reviews.edit',
            'reviews.delete',
            'reviews.respond',
            'users.view',
            'users.edit',
            'settings.view',
            'settings.edit',
            'analytics.view',
            'export.data'
        ]
    }],
    
    // Account Status
    isActive: {
        type: Boolean,
        default: true,
        index: true
    },
    
    isEmailVerified: {
        type: Boolean,
        default: false
    },
    
    emailVerificationToken: String,
    emailVerificationExpires: Date,
    
    // Security
    passwordResetToken: String,
    passwordResetExpires: Date,
    passwordChangedAt: Date,
    
    twoFactorEnabled: {
        type: Boolean,
        default: false
    },
    
    twoFactorSecret: String,
    
    // Login Tracking
    lastLogin: Date,
    lastLoginIP: String,
    loginAttempts: {
        type: Number,
        default: 0
    },
    lockUntil: Date,
    
    // Session Management
    refreshTokens: [{
        token: String,
        createdAt: {
            type: Date,
            default: Date.now
        },
        expiresAt: Date,
        userAgent: String,
        ipAddress: String
    }],
    
    // Activity Tracking
    lastActivity: Date,
    activityLog: [{
        action: String,
        resource: String,
        resourceId: String,
        timestamp: {
            type: Date,
            default: Date.now
        },
        ipAddress: String,
        userAgent: String
    }],
    
    // Preferences
    preferences: {
        theme: {
            type: String,
            enum: ['light', 'dark', 'auto'],
            default: 'light'
        },
        language: {
            type: String,
            default: 'en'
        },
        timezone: {
            type: String,
            default: 'Asia/Kolkata'
        },
        notifications: {
            email: {
                type: Boolean,
                default: true
            },
            browser: {
                type: Boolean,
                default: true
            },
            newBookings: {
                type: Boolean,
                default: true
            },
            newInquiries: {
                type: Boolean,
                default: true
            }
        }
    },
    
    // Timestamps
    createdAt: {
        type: Date,
        default: Date.now
    },
    
    updatedAt: {
        type: Date,
        default: Date.now
    },
    
    // Soft delete
    isDeleted: {
        type: Boolean,
        default: false,
        index: true
    },
    
    deletedAt: Date,
    deletedBy: String

}, {
    timestamps: true,
    toJSON: { 
        virtuals: true,
        transform: function(doc, ret) {
            delete ret.password;
            delete ret.passwordResetToken;
            delete ret.emailVerificationToken;
            delete ret.twoFactorSecret;
            delete ret.refreshTokens;
            return ret;
        }
    },
    toObject: { virtuals: true }
});

// Indexes for performance
userSchema.index({ username: 1 });
userSchema.index({ email: 1 });
userSchema.index({ role: 1, isActive: 1 });
userSchema.index({ isActive: 1, isDeleted: 1 });
userSchema.index({ lastLogin: -1 });

// Virtual fields
userSchema.virtual('fullName').get(function() {
    if (this.firstName && this.lastName) {
        return `${this.firstName} ${this.lastName}`;
    }
    return this.displayName || this.username;
});

userSchema.virtual('isLocked').get(function() {
    return !!(this.lockUntil && this.lockUntil > Date.now());
});

userSchema.virtual('hasPermission').get(function() {
    return (permission) => {
        if (this.role === 'super_admin') return true;
        return this.permissions.includes(permission);
    };
});

// Pre-save middleware
userSchema.pre('save', async function(next) {
    this.updatedAt = new Date();
    
    // Hash password if modified
    if (this.isModified('password')) {
        this.password = await bcrypt.hash(this.password, 12);
        this.passwordChangedAt = new Date();
    }
    
    // Set display name if not provided
    if (!this.displayName) {
        this.displayName = this.fullName || this.username;
    }
    
    next();
});

// Instance methods
userSchema.methods.comparePassword = async function(candidatePassword) {
    return await bcrypt.compare(candidatePassword, this.password);
};

userSchema.methods.generateAuthToken = function() {
    const payload = {
        id: this._id,
        username: this.username,
        email: this.email,
        role: this.role
    };
    
    return jwt.sign(payload, process.env.JWT_SECRET, {
        expiresIn: process.env.JWT_EXPIRES_IN || '24h'
    });
};

userSchema.methods.generateRefreshToken = function() {
    const refreshToken = jwt.sign(
        { id: this._id, type: 'refresh' },
        process.env.JWT_REFRESH_SECRET,
        { expiresIn: '7d' }
    );
    
    // Store refresh token
    this.refreshTokens.push({
        token: refreshToken,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
    });
    
    // Keep only last 5 refresh tokens
    if (this.refreshTokens.length > 5) {
        this.refreshTokens = this.refreshTokens.slice(-5);
    }
    
    return refreshToken;
};

userSchema.methods.revokeRefreshToken = function(token) {
    this.refreshTokens = this.refreshTokens.filter(rt => rt.token !== token);
    return this.save();
};

userSchema.methods.revokeAllRefreshTokens = function() {
    this.refreshTokens = [];
    return this.save();
};

userSchema.methods.recordLogin = function(ipAddress, userAgent) {
    this.lastLogin = new Date();
    this.lastLoginIP = ipAddress;
    this.lastActivity = new Date();
    this.loginAttempts = 0;
    this.lockUntil = undefined;
    
    this.logActivity('login', 'user', this._id, ipAddress, userAgent);
    
    return this.save();
};

userSchema.methods.recordFailedLogin = function() {
    this.loginAttempts += 1;
    
    // Lock account after 5 failed attempts for 30 minutes
    if (this.loginAttempts >= 5) {
        this.lockUntil = new Date(Date.now() + 30 * 60 * 1000);
    }
    
    return this.save();
};

userSchema.methods.logActivity = function(action, resource, resourceId, ipAddress, userAgent) {
    this.activityLog.push({
        action,
        resource,
        resourceId,
        ipAddress,
        userAgent
    });
    
    // Keep only last 100 activity logs
    if (this.activityLog.length > 100) {
        this.activityLog = this.activityLog.slice(-100);
    }
    
    this.lastActivity = new Date();
};

userSchema.methods.hasPermissionFor = function(permission) {
    if (this.role === 'super_admin') return true;
    return this.permissions.includes(permission);
};

userSchema.methods.activate = function() {
    this.isActive = true;
    return this.save();
};

userSchema.methods.deactivate = function() {
    this.isActive = false;
    this.revokeAllRefreshTokens();
    return this.save();
};

userSchema.methods.softDelete = function(deletedBy = 'system') {
    this.isDeleted = true;
    this.deletedAt = new Date();
    this.deletedBy = deletedBy;
    this.isActive = false;
    this.revokeAllRefreshTokens();
    return this.save();
};

// Static methods
userSchema.statics.findByUsername = function(username) {
    return this.findOne({ 
        username: username.toLowerCase(), 
        isActive: true, 
        isDeleted: false 
    });
};

userSchema.statics.findByEmail = function(email) {
    return this.findOne({ 
        email: email.toLowerCase(), 
        isActive: true, 
        isDeleted: false 
    });
};

userSchema.statics.findByCredentials = function(identifier) {
    return this.findOne({
        $or: [
            { username: identifier.toLowerCase() },
            { email: identifier.toLowerCase() }
        ],
        isActive: true,
        isDeleted: false
    }).select('+password');
};

userSchema.statics.createDefaultAdmin = async function() {
    const existingAdmin = await this.findOne({ role: 'super_admin' });
    if (existingAdmin) return existingAdmin;
    
    const defaultAdmin = new this({
        username: 'admin',
        email: '<EMAIL>',
        password: 'BuilderBallery2024!',
        firstName: 'System',
        lastName: 'Administrator',
        role: 'super_admin',
        isEmailVerified: true,
        permissions: [
            'bookings.view', 'bookings.create', 'bookings.edit', 'bookings.delete',
            'inquiries.view', 'inquiries.respond', 'inquiries.delete',
            'services.view', 'services.edit',
            'portfolio.view', 'portfolio.edit',
            'reviews.view', 'reviews.moderate', 'reviews.edit', 'reviews.delete', 'reviews.respond',
            'users.view', 'users.edit',
            'settings.view', 'settings.edit',
            'analytics.view', 'export.data'
        ]
    });
    
    return await defaultAdmin.save();
};

module.exports = mongoose.model('User', userSchema);
