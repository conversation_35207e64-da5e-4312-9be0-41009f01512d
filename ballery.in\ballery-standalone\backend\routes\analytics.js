const express = require('express');
const { query, validationResult } = require('express-validator');
const AnalyticsService = require('../services/analytics');
const { auth, requirePermission } = require('../middleware/auth');
const catchAsync = require('../utils/catchAsync');

const router = express.Router();

/**
 * @route   GET /api/analytics/dashboard
 * @desc    Get comprehensive dashboard analytics
 * @access  Private (Admin)
 */
router.get('/dashboard', [
    auth,
    requirePermission('analytics.view')
], [
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601(),
    query('period').optional().isIn(['7d', '30d', '90d', '1y', 'custom'])
], catchAsync(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
    }

    let { startDate, endDate, period = '30d' } = req.query;

    // Handle predefined periods
    if (period !== 'custom') {
        const now = new Date();
        endDate = now;
        
        switch (period) {
            case '7d':
                startDate = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));
                break;
            case '30d':
                startDate = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
                break;
            case '90d':
                startDate = new Date(now.getTime() - (90 * 24 * 60 * 60 * 1000));
                break;
            case '1y':
                startDate = new Date(now.getTime() - (365 * 24 * 60 * 60 * 1000));
                break;
        }
    }

    const analytics = await AnalyticsService.getDashboardAnalytics({
        startDate: startDate ? new Date(startDate) : undefined,
        endDate: endDate ? new Date(endDate) : undefined
    });

    res.json({
        success: true,
        data: analytics
    });
}));

/**
 * @route   GET /api/analytics/bookings
 * @desc    Get booking analytics
 * @access  Private (Admin)
 */
router.get('/bookings', [
    auth,
    requirePermission('analytics.view')
], [
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601(),
    query('groupBy').optional().isIn(['day', 'week', 'month'])
], catchAsync(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
    }

    const { startDate, endDate } = req.query;
    
    const analytics = await AnalyticsService.getBookingAnalytics(
        startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        endDate ? new Date(endDate) : new Date()
    );

    res.json({
        success: true,
        data: analytics
    });
}));

/**
 * @route   GET /api/analytics/revenue
 * @desc    Get revenue analytics
 * @access  Private (Admin)
 */
router.get('/revenue', [
    auth,
    requirePermission('analytics.view')
], [
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601(),
    query('currency').optional().isIn(['INR', 'USD'])
], catchAsync(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
    }

    const { startDate, endDate } = req.query;
    
    const analytics = await AnalyticsService.getRevenueAnalytics(
        startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        endDate ? new Date(endDate) : new Date()
    );

    res.json({
        success: true,
        data: analytics
    });
}));

/**
 * @route   GET /api/analytics/services
 * @desc    Get service performance analytics
 * @access  Private (Admin)
 */
router.get('/services', [
    auth,
    requirePermission('analytics.view')
], [
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601(),
    query('limit').optional().isInt({ min: 1, max: 50 }).toInt()
], catchAsync(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
    }

    const { startDate, endDate } = req.query;
    
    const analytics = await AnalyticsService.getServiceAnalytics(
        startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        endDate ? new Date(endDate) : new Date()
    );

    res.json({
        success: true,
        data: analytics
    });
}));

/**
 * @route   GET /api/analytics/customers
 * @desc    Get customer demographics and insights
 * @access  Private (Admin)
 */
router.get('/customers', [
    auth,
    requirePermission('analytics.view')
], [
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601()
], catchAsync(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
    }

    const { startDate, endDate } = req.query;
    
    const analytics = await AnalyticsService.getCustomerDemographics(
        startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        endDate ? new Date(endDate) : new Date()
    );

    res.json({
        success: true,
        data: analytics
    });
}));

/**
 * @route   GET /api/analytics/reviews
 * @desc    Get review analytics
 * @access  Private (Admin)
 */
router.get('/reviews', [
    auth,
    requirePermission('analytics.view')
], [
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601()
], catchAsync(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
    }

    const { startDate, endDate } = req.query;
    
    const analytics = await AnalyticsService.getReviewAnalytics(
        startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        endDate ? new Date(endDate) : new Date()
    );

    res.json({
        success: true,
        data: analytics
    });
}));

/**
 * @route   GET /api/analytics/conversions
 * @desc    Get conversion analytics
 * @access  Private (Admin)
 */
router.get('/conversions', [
    auth,
    requirePermission('analytics.view')
], [
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601()
], catchAsync(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
    }

    const { startDate, endDate } = req.query;
    
    const analytics = await AnalyticsService.getConversionAnalytics(
        startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        endDate ? new Date(endDate) : new Date()
    );

    res.json({
        success: true,
        data: analytics
    });
}));

/**
 * @route   GET /api/analytics/export
 * @desc    Export analytics data
 * @access  Private (Admin)
 */
router.get('/export', [
    auth,
    requirePermission('export.data')
], [
    query('type').isIn(['bookings', 'revenue', 'reviews', 'customers']),
    query('format').optional().isIn(['csv', 'json']),
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601()
], catchAsync(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
    }

    const { type, format = 'json', startDate, endDate } = req.query;
    
    let data;
    switch (type) {
        case 'bookings':
            data = await AnalyticsService.getBookingAnalytics(
                startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                endDate ? new Date(endDate) : new Date()
            );
            break;
        case 'revenue':
            data = await AnalyticsService.getRevenueAnalytics(
                startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                endDate ? new Date(endDate) : new Date()
            );
            break;
        case 'reviews':
            data = await AnalyticsService.getReviewAnalytics(
                startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                endDate ? new Date(endDate) : new Date()
            );
            break;
        case 'customers':
            data = await AnalyticsService.getCustomerDemographics(
                startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                endDate ? new Date(endDate) : new Date()
            );
            break;
    }

    if (format === 'csv') {
        // Convert to CSV format (simplified)
        const csv = JSON.stringify(data, null, 2);
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="${type}_analytics_${new Date().toISOString().split('T')[0]}.csv"`);
        res.send(csv);
    } else {
        res.json({
            success: true,
            data,
            exportedAt: new Date().toISOString()
        });
    }
}));

module.exports = router;
