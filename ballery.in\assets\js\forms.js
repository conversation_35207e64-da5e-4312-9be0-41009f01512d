/**
 * BUILDER BALLERY - Form Handler
 * Connects website forms to backend API
 */

// API Configuration
const API_BASE_URL = 'http://localhost:3000/api';

// Utility Functions
function showNotification(message, type = 'success') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-icon">${type === 'success' ? '✅' : '❌'}</span>
            <span class="notification-message">${message}</span>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
    `;
    
    // Add styles if not already added
    if (!document.getElementById('notification-styles')) {
        const styles = document.createElement('style');
        styles.id = 'notification-styles';
        styles.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                max-width: 400px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                animation: slideIn 0.3s ease-out;
            }
            
            .notification-success {
                background: #d4edda;
                border: 1px solid #c3e6cb;
                color: #155724;
            }
            
            .notification-error {
                background: #f8d7da;
                border: 1px solid #f5c6cb;
                color: #721c24;
            }
            
            .notification-content {
                display: flex;
                align-items: center;
                gap: 10px;
                padding: 15px;
            }
            
            .notification-icon {
                font-size: 18px;
            }
            
            .notification-message {
                flex: 1;
                font-weight: 500;
            }
            
            .notification-close {
                background: none;
                border: none;
                font-size: 20px;
                cursor: pointer;
                opacity: 0.7;
                padding: 0;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .notification-close:hover {
                opacity: 1;
            }
            
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(styles);
    }
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

function showFormLoading(form, loading = true) {
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    
    if (loading) {
        submitBtn.disabled = true;
        submitBtn.textContent = 'Sending...';
        submitBtn.style.opacity = '0.7';
        form.style.pointerEvents = 'none';
    } else {
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
        submitBtn.style.opacity = '1';
        form.style.pointerEvents = 'auto';
    }
}

// API Functions
async function submitInquiry(formData) {
    try {
        const response = await fetch(`${API_BASE_URL}/inquiries`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error('API Error:', error);
        throw error;
    }
}

async function submitCallback(formData) {
    try {
        const response = await fetch(`${API_BASE_URL}/inquiries/callback`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error('API Error:', error);
        throw error;
    }
}

// Form Handlers
function handleContactForm() {
    const form = document.getElementById('contact-form');
    if (!form) return;

    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        showFormLoading(form, true);
        
        try {
            // Get form data
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());
            
            // Map subject to inquiry type
            const subjectToType = {
                'general-inquiry': 'general',
                'service-question': 'general',
                'pricing-info': 'pricing',
                'technical-support': 'technical',
                'partnership': 'general',
                'feedback': 'general',
                'other': 'general'
            };
            
            // Prepare data for API
            const inquiryData = {
                name: data.name,
                email: data.email,
                phone: data.phone,
                subject: data.subject || 'General Inquiry',
                message: data.message,
                inquiryType: subjectToType[data.subject] || 'general'
            };
            
            // Submit to API
            const response = await submitInquiry(inquiryData);
            
            // Show success message
            showNotification('Message sent successfully! We will get back to you within 2 hours during business hours.', 'success');
            
            // Reset form
            form.reset();
            
            // Show success section if it exists
            const successDiv = document.getElementById('contact-success');
            if (successDiv) {
                successDiv.classList.remove('hidden');
                successDiv.scrollIntoView({ behavior: 'smooth' });
            }
            
        } catch (error) {
            console.error('Form submission error:', error);
            showNotification('Failed to send message. Please try again or contact us directly.', 'error');
        } finally {
            showFormLoading(form, false);
        }
    });
}

function handleCallbackForm() {
    const form = document.getElementById('callback-form');
    if (!form) return;

    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        showFormLoading(form, true);
        
        try {
            // Get form data
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());
            
            // Submit to API
            const response = await submitCallback(data);
            
            // Show success message
            showNotification('Callback request submitted successfully! We will call you back within 2 hours during business hours.', 'success');
            
            // Reset form
            form.reset();
            
        } catch (error) {
            console.error('Callback form submission error:', error);
            showNotification('Failed to submit callback request. Please try again or call us directly.', 'error');
        } finally {
            showFormLoading(form, false);
        }
    });
}

// Initialize forms when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    handleContactForm();
    handleCallbackForm();
    
    console.log('✅ Form handlers initialized - Connected to API at', API_BASE_URL);
});

// Export functions for use in other scripts
window.BallerForms = {
    submitInquiry,
    submitCallback,
    showNotification,
    showFormLoading
};
