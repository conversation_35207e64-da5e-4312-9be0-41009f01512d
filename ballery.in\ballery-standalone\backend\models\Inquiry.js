/**
 * BUILDER BALLERY - Inquiry Model
 * MongoDB schema for customer inquiries and contact forms
 */

const mongoose = require('mongoose');

const inquirySchema = new mongoose.Schema({
    inquiryId: {
        type: String,
        unique: true,
        default: () => 'INQ' + Date.now() + Math.random().toString(36).substr(2, 4).toUpperCase()
    },
    
    // Contact Information
    name: {
        type: String,
        required: true,
        trim: true,
        maxlength: 100
    },
    
    email: {
        type: String,
        required: false, // Made optional for callback requests
        trim: true,
        lowercase: true,
        match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
    },
    
    phone: {
        type: String,
        required: false, // Made optional since either email or phone should be provided
        trim: true,
        match: [/^[+]?[\d\s\-\(\)]{10,15}$/, 'Please enter a valid phone number']
    },
    
    // Inquiry Details
    subject: {
        type: String,
        trim: true,
        maxlength: 200,
        default: 'General Inquiry'
    },
    
    message: {
        type: String,
        required: true,
        trim: true,
        maxlength: 2000
    },
    
    inquiryType: {
        type: String,
        enum: ['general', 'service_info', 'pricing', 'booking', 'support', 'callback', 'complaint', 'feedback'],
        default: 'general',
        index: true
    },
    
    // Status Management
    status: {
        type: String,
        enum: ['unread', 'read', 'in-progress', 'responded', 'resolved', 'closed'],
        default: 'unread',
        index: true
    },
    
    priority: {
        type: String,
        enum: ['low', 'normal', 'high', 'urgent'],
        default: 'normal',
        index: true
    },
    
    // Assignment
    assignedTo: {
        type: String,
        trim: true
    },
    
    assignedAt: Date,
    
    // Response Information
    response: {
        type: String,
        trim: true,
        maxlength: 2000
    },
    
    respondedBy: {
        type: String,
        trim: true
    },
    
    respondedAt: Date,
    
    // Follow-up Information
    followUpRequired: {
        type: Boolean,
        default: false
    },
    
    followUpDate: Date,
    
    followUpNotes: {
        type: String,
        trim: true,
        maxlength: 500
    },
    
    // Communication History
    communications: [{
        type: {
            type: String,
            enum: ['email', 'phone', 'sms', 'whatsapp'],
            required: true
        },
        direction: {
            type: String,
            enum: ['inbound', 'outbound'],
            required: true
        },
        subject: String,
        message: String,
        timestamp: {
            type: Date,
            default: Date.now
        },
        staff: String
    }],
    
    // Tags for categorization
    tags: [{
        type: String,
        trim: true,
        lowercase: true
    }],
    
    // Source tracking
    source: {
        type: String,
        enum: ['website_contact', 'website_callback', 'phone', 'email', 'social_media', 'referral', 'other'],
        default: 'website_contact'
    },
    
    referrer: {
        type: String,
        trim: true
    },
    
    userAgent: {
        type: String,
        trim: true
    },
    
    ipAddress: {
        type: String,
        trim: true
    },
    
    // Customer Information (if available)
    customerInfo: {
        location: String,
        projectType: {
            type: String,
            enum: ['residential', 'commercial', 'industrial', 'renovation', 'other']
        },
        projectStage: {
            type: String,
            enum: ['planning', 'design', 'construction', 'finishing', 'completed']
        },
        budget: {
            type: String,
            enum: ['under_1L', '1L_5L', '5L_10L', '10L_25L', '25L_50L', 'above_50L', 'not_specified']
        },
        timeline: {
            type: String,
            enum: ['immediate', 'within_month', 'within_3months', 'within_6months', 'flexible']
        }
    },
    
    // Internal Notes
    internalNotes: {
        type: String,
        trim: true,
        maxlength: 1000
    },
    
    // Conversion tracking
    convertedToBooking: {
        type: Boolean,
        default: false
    },
    
    bookingId: {
        type: String,
        trim: true
    },
    
    conversionDate: Date,
    
    // Satisfaction tracking
    satisfactionRating: {
        type: Number,
        min: 1,
        max: 5
    },
    
    satisfactionComment: {
        type: String,
        trim: true,
        maxlength: 500
    },
    
    // Timestamps
    createdAt: {
        type: Date,
        default: Date.now,
        index: true
    },
    
    updatedAt: {
        type: Date,
        default: Date.now
    },
    
    readAt: Date,
    closedAt: Date,
    
    // Soft delete
    isDeleted: {
        type: Boolean,
        default: false,
        index: true
    },
    
    deletedAt: Date,
    deletedBy: String

}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Indexes for performance
inquirySchema.index({ inquiryId: 1 });
inquirySchema.index({ email: 1 });
inquirySchema.index({ status: 1, createdAt: -1 });
inquirySchema.index({ inquiryType: 1, status: 1 });
inquirySchema.index({ priority: 1, createdAt: -1 });
inquirySchema.index({ assignedTo: 1, status: 1 });
inquirySchema.index({ isDeleted: 1, createdAt: -1 });

// Virtual fields
inquirySchema.virtual('responseTime').get(function() {
    if (this.respondedAt && this.createdAt) {
        return Math.round((this.respondedAt - this.createdAt) / (1000 * 60 * 60)); // hours
    }
    return null;
});

inquirySchema.virtual('isOverdue').get(function() {
    if (this.status === 'unread' || this.status === 'read') {
        const hoursOld = (new Date() - this.createdAt) / (1000 * 60 * 60);
        return hoursOld > 24; // Consider overdue after 24 hours
    }
    return false;
});

inquirySchema.virtual('urgencyScore').get(function() {
    let score = 0;
    
    // Priority weight
    const priorityWeights = { low: 1, normal: 2, high: 3, urgent: 4 };
    score += priorityWeights[this.priority] || 2;
    
    // Age weight (older = more urgent)
    const hoursOld = (new Date() - this.createdAt) / (1000 * 60 * 60);
    if (hoursOld > 48) score += 3;
    else if (hoursOld > 24) score += 2;
    else if (hoursOld > 12) score += 1;
    
    // Type weight
    const typeWeights = { 
        complaint: 3, 
        urgent: 3, 
        booking: 2, 
        pricing: 2, 
        general: 1 
    };
    score += typeWeights[this.inquiryType] || 1;
    
    return score;
});

// Pre-save middleware
inquirySchema.pre('save', function(next) {
    this.updatedAt = new Date();

    // Validate that at least email or phone is provided
    if (!this.email && !this.phone) {
        const error = new Error('Either email or phone number must be provided');
        error.name = 'ValidationError';
        return next(error);
    }

    // Auto-generate inquiry ID if not provided
    if (!this.inquiryId) {
        this.inquiryId = 'INQ' + Date.now() + Math.random().toString(36).substr(2, 4).toUpperCase();
    }
    
    // Set read date when status changes from unread
    if (this.isModified('status') && this.status !== 'unread' && !this.readAt) {
        this.readAt = new Date();
    }
    
    // Set response date when response is added
    if (this.isModified('response') && this.response && !this.respondedAt) {
        this.respondedAt = new Date();
        if (this.status === 'unread' || this.status === 'read') {
            this.status = 'responded';
        }
    }
    
    // Set closed date when status changes to closed
    if (this.isModified('status') && this.status === 'closed' && !this.closedAt) {
        this.closedAt = new Date();
    }
    
    next();
});

// Static methods
inquirySchema.statics.findByInquiryId = function(inquiryId) {
    return this.findOne({ inquiryId, isDeleted: false });
};

inquirySchema.statics.findUnread = function() {
    return this.find({ 
        status: 'unread', 
        isDeleted: false 
    }).sort({ createdAt: -1 });
};

inquirySchema.statics.findOverdue = function() {
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    return this.find({
        status: { $in: ['unread', 'read'] },
        createdAt: { $lt: twentyFourHoursAgo },
        isDeleted: false
    }).sort({ createdAt: 1 });
};

inquirySchema.statics.getInquiryStats = function() {
    return this.aggregate([
        { $match: { isDeleted: false } },
        {
            $group: {
                _id: '$status',
                count: { $sum: 1 },
                avgResponseTime: { 
                    $avg: { 
                        $divide: [
                            { $subtract: ['$respondedAt', '$createdAt'] },
                            1000 * 60 * 60 // Convert to hours
                        ]
                    }
                }
            }
        }
    ]);
};

// Instance methods
inquirySchema.methods.addCommunication = function(communication) {
    this.communications.push(communication);
    return this.save();
};

inquirySchema.methods.markAsRead = function(readBy = 'system') {
    if (this.status === 'unread') {
        this.status = 'read';
        this.readAt = new Date();
    }
    return this.save();
};

inquirySchema.methods.respond = function(responseText, respondedBy = 'system') {
    this.response = responseText;
    this.respondedBy = respondedBy;
    this.respondedAt = new Date();
    this.status = 'responded';
    return this.save();
};

inquirySchema.methods.convertToBooking = function(bookingId) {
    this.convertedToBooking = true;
    this.bookingId = bookingId;
    this.conversionDate = new Date();
    this.status = 'resolved';
    return this.save();
};

inquirySchema.methods.softDelete = function(deletedBy = 'system') {
    this.isDeleted = true;
    this.deletedAt = new Date();
    this.deletedBy = deletedBy;
    return this.save();
};

module.exports = mongoose.model('Inquiry', inquirySchema);
