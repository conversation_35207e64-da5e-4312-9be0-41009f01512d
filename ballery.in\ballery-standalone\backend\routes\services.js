/**
 * BUILDER BALLERY - Services Routes
 * Handle service management and pricing
 */

const express = require('express');
const { body, validationResult, query } = require('express-validator');
const Service = require('../models/Service');
const { auth, requirePermission } = require('../middleware/auth');
const { catchAsync, AppError } = require('../middleware/errorHandler');

const router = express.Router();

/**
 * @route   GET /api/services
 * @desc    Get all services
 * @access  Public
 */
router.get('/', [
    query('serviceType').optional().isIn(['virtual', 'site-visit']),
    query('isActive').optional().isBoolean()
], catchAsync(async (req, res) => {
    const { serviceType, isActive = true } = req.query;

    const filter = { isDeleted: false };
    if (serviceType) filter.serviceType = serviceType;
    if (isActive !== undefined) filter.isActive = isActive === 'true';

    const services = await Service.find(filter)
        .sort({ displayOrder: 1, serviceName: 1 })
        .lean();

    res.json({
        success: true,
        data: { services }
    });
}));

/**
 * @route   POST /api/services
 * @desc    Create new service (admin only)
 * @access  Private (Admin)
 */
router.post('/', [
    auth,
    requirePermission('services.edit')
], [
    body('serviceName')
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('Service name must be between 2 and 100 characters'),
    body('serviceType')
        .isIn(['virtual', 'site-visit'])
        .withMessage('Service type must be virtual or site-visit'),
    body('price')
        .isFloat({ min: 0 })
        .withMessage('Price must be a positive number'),
    body('shortDescription')
        .trim()
        .isLength({ min: 10, max: 200 })
        .withMessage('Short description must be between 10 and 200 characters'),
    body('fullDescription')
        .optional()
        .trim()
        .isLength({ max: 1000 })
        .withMessage('Full description must be less than 1000 characters'),
    body('duration')
        .optional()
        .isInt({ min: 15, max: 480 })
        .withMessage('Duration must be between 15 and 480 minutes'),
    body('displayOrder')
        .optional()
        .isInt({ min: 0 })
        .withMessage('Display order must be a positive integer')
], catchAsync(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
    }

    const {
        serviceName,
        serviceType,
        price,
        shortDescription,
        fullDescription,
        duration,
        displayOrder,
        isActive = true
    } = req.body;

    // Check if service already exists
    const existingService = await Service.findOne({
        serviceName: serviceName,
        serviceType: serviceType,
        isDeleted: false
    });

    if (existingService) {
        return res.status(400).json({
            success: false,
            message: 'Service with this name and type already exists'
        });
    }

    const service = new Service({
        serviceName,
        serviceType,
        price,
        shortDescription,
        fullDescription,
        duration,
        displayOrder,
        isActive,
        createdBy: req.user._id
    });

    await service.save();

    res.status(201).json({
        success: true,
        message: 'Service created successfully',
        data: { service }
    });
}));

/**
 * @route   PUT /api/services/:id
 * @desc    Update service (admin only)
 * @access  Private (Admin)
 */
router.put('/:id', [
    auth,
    requirePermission('services.edit')
], [
    body('serviceName')
        .optional()
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('Service name must be between 2 and 100 characters'),
    body('price')
        .optional()
        .isFloat({ min: 0 })
        .withMessage('Price must be a positive number'),
    body('shortDescription')
        .optional()
        .trim()
        .isLength({ min: 10, max: 200 })
        .withMessage('Short description must be between 10 and 200 characters'),
    body('fullDescription')
        .optional()
        .trim()
        .isLength({ max: 1000 })
        .withMessage('Full description must be less than 1000 characters'),
    body('duration')
        .optional()
        .isInt({ min: 15, max: 480 })
        .withMessage('Duration must be between 15 and 480 minutes'),
    body('displayOrder')
        .optional()
        .isInt({ min: 0 })
        .withMessage('Display order must be a positive integer'),
    body('isActive')
        .optional()
        .isBoolean()
        .withMessage('isActive must be a boolean')
], catchAsync(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
    }

    const { id } = req.params;
    const updates = req.body;

    const service = await Service.findById(id);
    
    if (!service || service.isDeleted) {
        throw new AppError('Service not found', 404);
    }

    // Update service
    Object.keys(updates).forEach(key => {
        if (updates[key] !== undefined) {
            service[key] = updates[key];
        }
    });

    service.updatedBy = req.user._id;
    await service.save();

    res.json({
        success: true,
        message: 'Service updated successfully',
        data: { service }
    });
}));

/**
 * @route   DELETE /api/services/:id
 * @desc    Delete service (admin only)
 * @access  Private (Admin)
 */
router.delete('/:id', [
    auth,
    requirePermission('services.edit')
], catchAsync(async (req, res) => {
    const { id } = req.params;

    const service = await Service.findById(id);
    
    if (!service || service.isDeleted) {
        throw new AppError('Service not found', 404);
    }

    // Soft delete
    service.isDeleted = true;
    service.deletedAt = new Date();
    service.deletedBy = req.user._id;
    await service.save();

    res.json({
        success: true,
        message: 'Service deleted successfully'
    });
}));

/**
 * @route   GET /api/services/stats
 * @desc    Get service statistics
 * @access  Private (Admin)
 */
router.get('/stats', [
    auth,
    requirePermission('services.view')
], catchAsync(async (req, res) => {
    const stats = await Service.aggregate([
        { $match: { isDeleted: false } },
        {
            $group: {
                _id: '$serviceType',
                count: { $sum: 1 },
                totalRevenue: { $sum: { $multiply: ['$price', '$metrics.totalBookings'] } },
                avgPrice: { $avg: '$price' }
            }
        }
    ]);

    const totalServices = await Service.countDocuments({ isDeleted: false });
    const activeServices = await Service.countDocuments({ isDeleted: false, isActive: true });

    res.json({
        success: true,
        data: {
            totalServices,
            activeServices,
            byType: stats
        }
    });
}));

module.exports = router;
