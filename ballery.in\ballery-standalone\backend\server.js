/**
 * BUILDER BALLERY - Node.js Backend Server
 * Express.js server with MongoDB integration
 */

const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

// Import middleware
const { errorHandler } = require('./middleware/errorHandler');
const notFound = require('./middleware/notFound');

// Import routes
const authRoutes = require('./routes/auth');
const bookingRoutes = require('./routes/bookings');
const inquiryRoutes = require('./routes/inquiries');
const serviceRoutes = require('./routes/services');

// Create Express app
const app = express();

// Environment variables
const PORT = process.env.PORT || 3000;
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/builder_ballery';
const NODE_ENV = process.env.NODE_ENV || 'development';

// Security middleware
app.use(helmet({
    crossOriginEmbedderPolicy: false,
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
            fontSrc: ["'self'", "https://fonts.gstatic.com"],
            imgSrc: ["'self'", "data:", "https:"],
            scriptSrc: ["'self'"],
            connectSrc: ["'self'"]
        }
    }
}));

// Rate limiting
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: NODE_ENV === 'production' ? 100 : 1000, // Limit each IP to 100 requests per windowMs in production
    message: {
        error: 'Too many requests from this IP, please try again later.',
        retryAfter: '15 minutes'
    },
    standardHeaders: true,
    legacyHeaders: false
});

app.use('/api/', limiter);

// CORS configuration
const corsOptions = {
    origin: function (origin, callback) {
        // Allow requests with no origin (mobile apps, Postman, etc.)
        if (!origin) return callback(null, true);
        
        const allowedOrigins = [
            'http://localhost:3000',
            'http://localhost:8080',
            'http://127.0.0.1:3000',
            'http://127.0.0.1:8080',
            'https://builderballery.com',
            'https://www.builderballery.com'
        ];
        
        if (NODE_ENV === 'development') {
            // Allow all origins in development
            return callback(null, true);
        }
        
        if (allowedOrigins.indexOf(origin) !== -1) {
            callback(null, true);
        } else {
            callback(new Error('Not allowed by CORS'));
        }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

app.use(cors(corsOptions));

// Compression middleware
app.use(compression());

// Logging middleware
if (NODE_ENV === 'development') {
    app.use(morgan('dev'));
} else {
    app.use(morgan('combined'));
}

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static files middleware (for uploaded files)
app.use('/uploads', express.static('uploads'));

// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: NODE_ENV,
        version: process.env.npm_package_version || '1.0.0'
    });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/bookings', bookingRoutes);
app.use('/api/inquiries', inquiryRoutes);
app.use('/api/services', serviceRoutes);

// Root endpoint
app.get('/', (req, res) => {
    res.json({
        message: 'BUILDER BALLERY API Server',
        version: '1.0.0',
        status: 'Running',
        documentation: '/api/docs',
        health: '/health'
    });
});

// API documentation endpoint
app.get('/api/docs', (req, res) => {
    res.json({
        title: 'BUILDER BALLERY API Documentation',
        version: '1.0.0',
        baseURL: req.protocol + '://' + req.get('host') + '/api',
        endpoints: {
            auth: {
                'POST /auth/login': 'Admin login',
                'POST /auth/logout': 'Admin logout',
                'GET /auth/verify': 'Verify token'
            },
            bookings: {
                'GET /bookings': 'Get all bookings (admin)',
                'POST /bookings': 'Create new booking',
                'GET /bookings/:id': 'Get booking by ID',
                'PUT /bookings/:id': 'Update booking',
                'DELETE /bookings/:id': 'Delete booking (admin)'
            },
            inquiries: {
                'GET /inquiries': 'Get all inquiries (admin)',
                'POST /inquiries': 'Submit new inquiry',
                'POST /inquiries/callback': 'Submit callback request',
                'PUT /inquiries/:id/respond': 'Respond to inquiry (admin)'
            },
            services: {
                'GET /services': 'Get all services',
                'POST /services': 'Create service (admin)',
                'PUT /services/:id': 'Update service (admin)',
                'DELETE /services/:id': 'Delete service (admin)'
            },
            portfolio: {
                'GET /portfolio': 'Get portfolio projects',
                'POST /portfolio': 'Add project (admin)',
                'PUT /portfolio/:id': 'Update project (admin)',
                'DELETE /portfolio/:id': 'Delete project (admin)'
            },
            testimonials: {
                'GET /testimonials': 'Get testimonials',
                'POST /testimonials': 'Add testimonial (admin)',
                'PUT /testimonials/:id': 'Update testimonial (admin)',
                'DELETE /testimonials/:id': 'Delete testimonial (admin)'
            },
            stats: {
                'GET /stats/public': 'Get public statistics',
                'GET /stats/admin': 'Get admin statistics (admin)'
            },
            admin: {
                'GET /admin/dashboard': 'Get dashboard data (admin)',
                'GET /admin/analytics': 'Get analytics data (admin)',
                'GET /admin/export/:type': 'Export data (admin)'
            }
        }
    });
});

// Error handling middleware
app.use(notFound);
app.use(errorHandler);

// Database connection
mongoose.connect(MONGODB_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
})
.then(() => {
    console.log('✅ Connected to MongoDB');
    console.log(`📊 Database: ${mongoose.connection.name}`);
})
.catch((error) => {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('🔄 SIGTERM received. Shutting down gracefully...');
    mongoose.connection.close(() => {
        console.log('📊 MongoDB connection closed.');
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('🔄 SIGINT received. Shutting down gracefully...');
    mongoose.connection.close(() => {
        console.log('📊 MongoDB connection closed.');
        process.exit(0);
    });
});

// Start server
const server = app.listen(PORT, () => {
    console.log('🚀 BUILDER BALLERY API Server Started');
    console.log(`🌐 Server running on port ${PORT}`);
    console.log(`📍 Environment: ${NODE_ENV}`);
    console.log(`🔗 API Base URL: http://localhost:${PORT}/api`);
    console.log(`📚 Documentation: http://localhost:${PORT}/api/docs`);
    console.log(`❤️  Health Check: http://localhost:${PORT}/health`);
    
    if (NODE_ENV === 'development') {
        console.log('🔧 Development mode - CORS allows all origins');
    }
});

// Handle server errors
server.on('error', (error) => {
    if (error.code === 'EADDRINUSE') {
        console.error(`❌ Port ${PORT} is already in use`);
        process.exit(1);
    } else {
        console.error('❌ Server error:', error);
    }
});

module.exports = app;
// Updated with new routes
