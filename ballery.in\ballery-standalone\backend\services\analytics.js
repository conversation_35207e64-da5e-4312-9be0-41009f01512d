const Booking = require('../models/Booking');
const Inquiry = require('../models/Inquiry');
const Service = require('../models/Service');
const Review = require('../models/Review');
const Portfolio = require('../models/Portfolio');

/**
 * Analytics Service for Builder Ballery
 * Provides comprehensive analytics and reporting functionality
 */
class AnalyticsService {
    
    /**
     * Get comprehensive dashboard analytics
     */
    static async getDashboardAnalytics(dateRange = {}) {
        const { startDate, endDate } = this.getDateRange(dateRange);
        
        const [
            bookingStats,
            revenueStats,
            inquiryStats,
            reviewStats,
            serviceStats,
            conversionStats
        ] = await Promise.all([
            this.getBookingAnalytics(startDate, endDate),
            this.getRevenueAnalytics(startDate, endDate),
            this.getInquiryAnalytics(startDate, endDate),
            this.getReviewAnalytics(startDate, endDate),
            this.getServiceAnalytics(startDate, endDate),
            this.getConversionAnalytics(startDate, endDate)
        ]);

        return {
            bookings: bookingStats,
            revenue: revenueStats,
            inquiries: inquiryStats,
            reviews: reviewStats,
            services: serviceStats,
            conversions: conversionStats,
            dateRange: { startDate, endDate }
        };
    }

    /**
     * Get booking analytics
     */
    static async getBookingAnalytics(startDate, endDate) {
        const matchStage = {
            isDeleted: false,
            createdAt: { $gte: startDate, $lte: endDate }
        };

        const [totalBookings, statusBreakdown, dailyBookings, serviceTypeBreakdown] = await Promise.all([
            Booking.countDocuments(matchStage),
            
            Booking.aggregate([
                { $match: matchStage },
                {
                    $group: {
                        _id: '$status',
                        count: { $sum: 1 },
                        totalValue: { $sum: '$totalAmount' }
                    }
                }
            ]),
            
            Booking.aggregate([
                { $match: matchStage },
                {
                    $group: {
                        _id: {
                            date: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } }
                        },
                        count: { $sum: 1 },
                        value: { $sum: '$totalAmount' }
                    }
                },
                { $sort: { '_id.date': 1 } }
            ]),
            
            Booking.aggregate([
                { $match: matchStage },
                {
                    $group: {
                        _id: '$serviceType',
                        count: { $sum: 1 },
                        totalValue: { $sum: '$totalAmount' },
                        avgValue: { $avg: '$totalAmount' }
                    }
                }
            ])
        ]);

        return {
            total: totalBookings,
            statusBreakdown,
            dailyTrend: dailyBookings,
            serviceTypeBreakdown
        };
    }

    /**
     * Get revenue analytics
     */
    static async getRevenueAnalytics(startDate, endDate) {
        const matchStage = {
            isDeleted: false,
            createdAt: { $gte: startDate, $lte: endDate }
        };

        const [revenueStats, monthlyRevenue, serviceRevenue] = await Promise.all([
            Booking.aggregate([
                { $match: matchStage },
                {
                    $group: {
                        _id: null,
                        totalRevenue: { $sum: '$totalAmount' },
                        paidRevenue: { $sum: '$paidAmount' },
                        pendingRevenue: { $sum: { $subtract: ['$totalAmount', '$paidAmount'] } },
                        avgBookingValue: { $avg: '$totalAmount' },
                        count: { $sum: 1 }
                    }
                }
            ]),
            
            Booking.aggregate([
                { $match: matchStage },
                {
                    $group: {
                        _id: {
                            year: { $year: '$createdAt' },
                            month: { $month: '$createdAt' }
                        },
                        revenue: { $sum: '$totalAmount' },
                        paidRevenue: { $sum: '$paidAmount' },
                        bookingCount: { $sum: 1 }
                    }
                },
                { $sort: { '_id.year': 1, '_id.month': 1 } }
            ]),
            
            Booking.aggregate([
                { $match: matchStage },
                {
                    $group: {
                        _id: '$serviceType',
                        revenue: { $sum: '$totalAmount' },
                        paidRevenue: { $sum: '$paidAmount' },
                        bookingCount: { $sum: 1 },
                        avgValue: { $avg: '$totalAmount' }
                    }
                },
                { $sort: { revenue: -1 } }
            ])
        ]);

        return {
            summary: revenueStats[0] || {
                totalRevenue: 0,
                paidRevenue: 0,
                pendingRevenue: 0,
                avgBookingValue: 0,
                count: 0
            },
            monthlyTrend: monthlyRevenue,
            serviceBreakdown: serviceRevenue
        };
    }

    /**
     * Get inquiry analytics
     */
    static async getInquiryAnalytics(startDate, endDate) {
        const matchStage = {
            isDeleted: false,
            createdAt: { $gte: startDate, $lte: endDate }
        };

        const [totalInquiries, statusBreakdown, typeBreakdown, conversionRate] = await Promise.all([
            Inquiry.countDocuments(matchStage),
            
            Inquiry.aggregate([
                { $match: matchStage },
                {
                    $group: {
                        _id: '$status',
                        count: { $sum: 1 }
                    }
                }
            ]),
            
            Inquiry.aggregate([
                { $match: matchStage },
                {
                    $group: {
                        _id: '$inquiryType',
                        count: { $sum: 1 }
                    }
                }
            ]),
            
            Inquiry.aggregate([
                { $match: matchStage },
                {
                    $group: {
                        _id: null,
                        totalInquiries: { $sum: 1 },
                        convertedInquiries: {
                            $sum: {
                                $cond: [{ $ne: ['$conversionDate', null] }, 1, 0]
                            }
                        }
                    }
                }
            ])
        ]);

        const conversionRatePercent = conversionRate[0] ? 
            ((conversionRate[0].convertedInquiries / conversionRate[0].totalInquiries) * 100).toFixed(1) : 0;

        return {
            total: totalInquiries,
            statusBreakdown,
            typeBreakdown,
            conversionRate: conversionRatePercent
        };
    }

    /**
     * Get review analytics
     */
    static async getReviewAnalytics(startDate, endDate) {
        const matchStage = {
            isDeleted: false,
            submittedAt: { $gte: startDate, $lte: endDate }
        };

        const [totalReviews, ratingDistribution, statusBreakdown, avgRating] = await Promise.all([
            Review.countDocuments(matchStage),
            
            Review.aggregate([
                { $match: matchStage },
                {
                    $group: {
                        _id: '$rating',
                        count: { $sum: 1 }
                    }
                },
                { $sort: { _id: -1 } }
            ]),
            
            Review.aggregate([
                { $match: matchStage },
                {
                    $group: {
                        _id: '$status',
                        count: { $sum: 1 }
                    }
                }
            ]),
            
            Review.aggregate([
                { $match: { ...matchStage, status: 'approved' } },
                {
                    $group: {
                        _id: null,
                        avgRating: { $avg: '$rating' },
                        count: { $sum: 1 }
                    }
                }
            ])
        ]);

        return {
            total: totalReviews,
            ratingDistribution,
            statusBreakdown,
            averageRating: avgRating[0] ? avgRating[0].avgRating.toFixed(1) : 0
        };
    }

    /**
     * Get service analytics
     */
    static async getServiceAnalytics(startDate, endDate) {
        const [popularServices, servicePerformance] = await Promise.all([
            Booking.aggregate([
                {
                    $match: {
                        isDeleted: false,
                        createdAt: { $gte: startDate, $lte: endDate }
                    }
                },
                {
                    $group: {
                        _id: '$serviceType',
                        bookingCount: { $sum: 1 },
                        totalRevenue: { $sum: '$totalAmount' },
                        avgValue: { $avg: '$totalAmount' }
                    }
                },
                { $sort: { bookingCount: -1 } }
            ]),
            
            Service.aggregate([
                {
                    $lookup: {
                        from: 'bookings',
                        localField: '_id',
                        foreignField: 'serviceId',
                        as: 'bookings'
                    }
                },
                {
                    $lookup: {
                        from: 'reviews',
                        localField: '_id',
                        foreignField: 'serviceId',
                        as: 'reviews'
                    }
                },
                {
                    $project: {
                        serviceName: 1,
                        serviceType: 1,
                        price: 1,
                        bookingCount: { $size: '$bookings' },
                        reviewCount: { $size: '$reviews' },
                        avgRating: { $avg: '$reviews.rating' }
                    }
                },
                { $sort: { bookingCount: -1 } }
            ])
        ]);

        return {
            popularServices,
            servicePerformance
        };
    }

    /**
     * Get conversion analytics
     */
    static async getConversionAnalytics(startDate, endDate) {
        const [inquiryToBooking, leadSources] = await Promise.all([
            Inquiry.aggregate([
                {
                    $match: {
                        isDeleted: false,
                        createdAt: { $gte: startDate, $lte: endDate }
                    }
                },
                {
                    $group: {
                        _id: null,
                        totalInquiries: { $sum: 1 },
                        convertedInquiries: {
                            $sum: {
                                $cond: [{ $ne: ['$conversionDate', null] }, 1, 0]
                            }
                        }
                    }
                }
            ]),
            
            Inquiry.aggregate([
                {
                    $match: {
                        isDeleted: false,
                        createdAt: { $gte: startDate, $lte: endDate }
                    }
                },
                {
                    $group: {
                        _id: '$source',
                        count: { $sum: 1 },
                        converted: {
                            $sum: {
                                $cond: [{ $ne: ['$conversionDate', null] }, 1, 0]
                            }
                        }
                    }
                }
            ])
        ]);

        const conversionRate = inquiryToBooking[0] ? 
            ((inquiryToBooking[0].convertedInquiries / inquiryToBooking[0].totalInquiries) * 100).toFixed(1) : 0;

        return {
            inquiryToBookingRate: conversionRate,
            leadSources: leadSources.map(source => ({
                ...source,
                conversionRate: source.count > 0 ? ((source.converted / source.count) * 100).toFixed(1) : 0
            }))
        };
    }

    /**
     * Get customer demographics
     */
    static async getCustomerDemographics(startDate, endDate) {
        const [locationStats, servicePreferences, bookingPatterns] = await Promise.all([
            Booking.aggregate([
                {
                    $match: {
                        isDeleted: false,
                        createdAt: { $gte: startDate, $lte: endDate },
                        'projectDetails.location': { $exists: true, $ne: '' }
                    }
                },
                {
                    $group: {
                        _id: '$projectDetails.location',
                        count: { $sum: 1 },
                        totalValue: { $sum: '$totalAmount' }
                    }
                },
                { $sort: { count: -1 } },
                { $limit: 10 }
            ]),
            
            Booking.aggregate([
                {
                    $match: {
                        isDeleted: false,
                        createdAt: { $gte: startDate, $lte: endDate }
                    }
                },
                {
                    $group: {
                        _id: '$serviceType',
                        count: { $sum: 1 },
                        uniqueCustomers: { $addToSet: '$customerEmail' }
                    }
                },
                {
                    $project: {
                        count: 1,
                        uniqueCustomers: { $size: '$uniqueCustomers' }
                    }
                }
            ]),
            
            Booking.aggregate([
                {
                    $match: {
                        isDeleted: false,
                        createdAt: { $gte: startDate, $lte: endDate }
                    }
                },
                {
                    $group: {
                        _id: {
                            hour: { $hour: '$createdAt' },
                            dayOfWeek: { $dayOfWeek: '$createdAt' }
                        },
                        count: { $sum: 1 }
                    }
                }
            ])
        ]);

        return {
            topLocations: locationStats,
            servicePreferences,
            bookingPatterns
        };
    }

    /**
     * Helper method to get date range
     */
    static getDateRange(dateRange) {
        const now = new Date();
        let startDate, endDate;

        if (dateRange.startDate && dateRange.endDate) {
            startDate = new Date(dateRange.startDate);
            endDate = new Date(dateRange.endDate);
        } else {
            // Default to last 30 days
            endDate = now;
            startDate = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
        }

        return { startDate, endDate };
    }
}

module.exports = AnalyticsService;
