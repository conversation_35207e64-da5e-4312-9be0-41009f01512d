<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Builder Ballery - Live Admin Panel</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f1f1f1;
            color: #333;
        }

        .admin-header {
            background: #2c5aa0;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .admin-logo {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .admin-user {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .admin-sidebar {
            position: fixed;
            left: 0;
            top: 70px;
            width: 250px;
            height: calc(100vh - 70px);
            background: white;
            box-shadow: 2px 0 4px rgba(0,0,0,0.1);
            overflow-y: auto;
        }

        .admin-nav {
            list-style: none;
            padding: 20px 0;
        }

        .admin-nav li {
            margin: 0;
        }

        .admin-nav a {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 20px;
            color: #333;
            text-decoration: none;
            transition: background-color 0.2s ease;
        }

        .admin-nav a:hover,
        .admin-nav a.active {
            background: #f8f9fa;
            color: #2c5aa0;
        }

        .admin-content {
            margin-left: 250px;
            padding: 20px;
            min-height: calc(100vh - 70px);
        }

        .page-header {
            background: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-header h1 {
            color: #2c5aa0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #2c5aa0;
            color: white;
        }

        .btn-primary:hover {
            background: #1e3f73;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 15px;
            transition: transform 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-icon {
            font-size: 2.5rem;
            opacity: 0.8;
        }

        .stat-content h3 {
            margin: 0;
            font-size: 2rem;
            font-weight: bold;
            color: #2c5aa0;
        }

        .stat-content p {
            margin: 5px 0;
            font-weight: 600;
            color: #333;
        }

        .data-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-content {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pending { background: #fff3cd; color: #856404; }
        .status-confirmed { background: #d4edda; color: #155724; }
        .status-completed { background: #cce5ff; color: #004085; }
        .status-unread { background: #fff3cd; color: #856404; }
        .status-read { background: #d4edda; color: #155724; }
        .status-responded { background: #cce5ff; color: #004085; }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .form-input,
        .form-textarea,
        .form-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #999;
        }

        .close:hover {
            color: #333;
        }

        .admin-page {
            display: none;
        }

        .admin-page.active {
            display: block;
        }

        /* Reviews and Analytics Specific Styles */
        .page-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .filters-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-group label {
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }

        .filter-group select,
        .filter-group input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .analytics-section {
            margin-bottom: 30px;
        }

        .analytics-section h2 {
            color: #2c5aa0;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .analytics-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .analytics-card h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
        }

        .chart-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }

        .table-actions {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .table-actions input[type="checkbox"] {
            margin: 0;
        }

        .table-actions label {
            margin: 0;
            font-size: 14px;
            color: #666;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pending { background: #fff3cd; color: #856404; }
        .status-approved { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        .status-hidden { background: #e2e3e5; color: #383d41; }
        .status-unread { background: #cce5ff; color: #004085; }
        .status-read { background: #e7f3ff; color: #0056b3; }
        .status-responded { background: #d1ecf1; color: #0c5460; }
        .status-resolved { background: #d4edda; color: #155724; }

        .rating-stars {
            color: #ffc107;
            font-size: 16px;
        }

        @media (max-width: 768px) {
            .admin-sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .admin-content {
                margin-left: 0;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .filters-section {
                grid-template-columns: 1fr;
            }

            .analytics-grid {
                grid-template-columns: 1fr;
            }

            .page-actions {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <!-- Admin Header -->
    <header class="admin-header">
        <div class="admin-logo">
            🏗️ BUILDER BALLERY - Live Admin Panel
        </div>
        <div class="admin-user">
            <span>🔑 Demo Token Active</span>
            <span id="connection-status">🟢</span>
        </div>
    </header>

    <!-- Admin Sidebar -->
    <nav class="admin-sidebar">
        <ul class="admin-nav">
            <li><a href="#dashboard" class="nav-link active" data-page="dashboard">📊 Dashboard</a></li>
            <li><a href="#bookings" class="nav-link" data-page="bookings">📅 Bookings</a></li>
            <li><a href="#inquiries" class="nav-link" data-page="inquiries">💬 Inquiries</a></li>
            <li><a href="#services" class="nav-link" data-page="services">🛠️ Services</a></li>
            <li><a href="#reviews" class="nav-link" data-page="reviews">⭐ Reviews</a></li>
            <li><a href="#analytics" class="nav-link" data-page="analytics">📈 Analytics</a></li>
            <li><a href="#add-data" class="nav-link" data-page="add-data">➕ Add Test Data</a></li>
        </ul>
    </nav>

    <!-- Main Content -->
    <main class="admin-content">
        <!-- Dashboard Page -->
        <div id="dashboard-page" class="admin-page active">
            <div class="page-header">
                <h1>📊 Live Dashboard</h1>
                <button class="btn btn-primary" onclick="refreshDashboard()">🔄 Refresh Data</button>
            </div>

            <!-- Statistics Grid -->
            <div class="stats-grid" id="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">📅</div>
                    <div class="stat-content">
                        <h3 id="total-bookings">-</h3>
                        <p>Total Bookings</p>
                        <small>Loading...</small>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">💬</div>
                    <div class="stat-content">
                        <h3 id="total-inquiries">-</h3>
                        <p>Total Inquiries</p>
                        <small>Loading...</small>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">🛠️</div>
                    <div class="stat-content">
                        <h3 id="total-services">-</h3>
                        <p>Active Services</p>
                        <small>Loading...</small>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">🔗</div>
                    <div class="stat-content">
                        <h3 id="api-status">✅</h3>
                        <p>API Status</p>
                        <small>Connected</small>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="data-table">
                <div class="table-header">
                    <h2>Recent Activity</h2>
                </div>
                <div class="table-content">
                    <div id="recent-activity" class="loading">Loading recent activity...</div>
                </div>
            </div>
        </div>

        <!-- Bookings Page -->
        <div id="bookings-page" class="admin-page">
            <div class="page-header">
                <h1>📅 Bookings Management</h1>
                <button class="btn btn-primary" onclick="refreshBookings()">🔄 Refresh</button>
            </div>

            <div class="data-table">
                <div class="table-header">
                    <h2>All Bookings</h2>
                </div>
                <div class="table-content">
                    <div id="bookings-content" class="loading">Loading bookings...</div>
                </div>
            </div>
        </div>

        <!-- Inquiries Page -->
        <div id="inquiries-page" class="admin-page">
            <div class="page-header">
                <h1>💬 Customer Inquiries</h1>
                <button class="btn btn-primary" onclick="refreshInquiries()">🔄 Refresh</button>
            </div>

            <div class="data-table">
                <div class="table-header">
                    <h2>All Inquiries</h2>
                </div>
                <div class="table-content">
                    <div id="inquiries-content" class="loading">Loading inquiries...</div>
                </div>
            </div>
        </div>

        <!-- Services Page -->
        <div id="services-page" class="admin-page">
            <div class="page-header">
                <h1>🛠️ Services Management</h1>
                <button class="btn btn-primary" onclick="refreshServices()">🔄 Refresh</button>
            </div>

            <div class="data-table">
                <div class="table-header">
                    <h2>All Services</h2>
                </div>
                <div class="table-content">
                    <div id="services-content" class="loading">Loading services...</div>
                </div>
            </div>
        </div>

        <!-- Reviews Page -->
        <div id="reviews-page" class="admin-page">
            <div class="page-header">
                <h1>⭐ Reviews Management</h1>
                <div class="page-actions">
                    <button class="btn btn-primary" onclick="refreshReviews()">🔄 Refresh</button>
                    <button class="btn btn-success" onclick="bulkApproveReviews()" id="bulk-approve-btn" disabled>✅ Bulk Approve</button>
                    <button class="btn btn-danger" onclick="bulkRejectReviews()" id="bulk-reject-btn" disabled>❌ Bulk Reject</button>
                </div>
            </div>

            <!-- Review Filters -->
            <div class="filters-section">
                <div class="filter-group">
                    <label>Status:</label>
                    <select id="review-status-filter" onchange="filterReviews()">
                        <option value="">All Statuses</option>
                        <option value="pending">Pending</option>
                        <option value="approved">Approved</option>
                        <option value="rejected">Rejected</option>
                        <option value="hidden">Hidden</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>Rating:</label>
                    <select id="review-rating-filter" onchange="filterReviews()">
                        <option value="">All Ratings</option>
                        <option value="5">5 Stars</option>
                        <option value="4">4 Stars</option>
                        <option value="3">3 Stars</option>
                        <option value="2">2 Stars</option>
                        <option value="1">1 Star</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>Service Type:</label>
                    <select id="review-service-filter" onchange="filterReviews()">
                        <option value="">All Services</option>
                        <option value="virtual">Virtual</option>
                        <option value="site-visit">Site Visit</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>Search:</label>
                    <input type="text" id="review-search" placeholder="Search reviews..." onkeyup="searchReviews()">
                </div>
            </div>

            <!-- Review Statistics -->
            <div class="stats-grid" id="review-stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">⭐</div>
                    <div class="stat-content">
                        <h3 id="total-reviews">-</h3>
                        <p>Total Reviews</p>
                        <small>Loading...</small>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">⏳</div>
                    <div class="stat-content">
                        <h3 id="pending-reviews">-</h3>
                        <p>Pending Reviews</p>
                        <small>Loading...</small>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-content">
                        <h3 id="approved-reviews">-</h3>
                        <p>Approved Reviews</p>
                        <small>Loading...</small>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📊</div>
                    <div class="stat-content">
                        <h3 id="average-rating">-</h3>
                        <p>Average Rating</p>
                        <small>Loading...</small>
                    </div>
                </div>
            </div>

            <div class="data-table">
                <div class="table-header">
                    <h2>All Reviews</h2>
                    <div class="table-actions">
                        <input type="checkbox" id="select-all-reviews" onchange="toggleSelectAllReviews()">
                        <label for="select-all-reviews">Select All</label>
                    </div>
                </div>
                <div class="table-content">
                    <div id="reviews-content" class="loading">Loading reviews...</div>
                </div>
            </div>
        </div>

        <!-- Analytics Page -->
        <div id="analytics-page" class="admin-page">
            <div class="page-header">
                <h1>📈 Analytics Dashboard</h1>
                <div class="page-actions">
                    <button class="btn btn-primary" onclick="refreshAnalytics()">🔄 Refresh</button>
                    <select id="analytics-period" onchange="changeAnalyticsPeriod()">
                        <option value="7d">Last 7 Days</option>
                        <option value="30d" selected>Last 30 Days</option>
                        <option value="90d">Last 90 Days</option>
                        <option value="1y">Last Year</option>
                    </select>
                </div>
            </div>

            <!-- Key Metrics -->
            <div class="analytics-section">
                <h2>📊 Key Metrics</h2>
                <div class="stats-grid" id="analytics-key-metrics">
                    <div class="stat-card">
                        <div class="stat-icon">💰</div>
                        <div class="stat-content">
                            <h3 id="total-revenue">-</h3>
                            <p>Total Revenue</p>
                            <small id="revenue-change">Loading...</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">📅</div>
                        <div class="stat-content">
                            <h3 id="analytics-bookings">-</h3>
                            <p>Total Bookings</p>
                            <small id="bookings-change">Loading...</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">📈</div>
                        <div class="stat-content">
                            <h3 id="conversion-rate">-</h3>
                            <p>Conversion Rate</p>
                            <small id="conversion-change">Loading...</small>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">⭐</div>
                        <div class="stat-content">
                            <h3 id="analytics-rating">-</h3>
                            <p>Avg Rating</p>
                            <small id="rating-change">Loading...</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="analytics-section">
                <h2>📊 Revenue & Bookings Trends</h2>
                <div class="chart-container">
                    <canvas id="revenue-chart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- Service Performance -->
            <div class="analytics-section">
                <h2>🛠️ Service Performance</h2>
                <div class="analytics-grid">
                    <div class="analytics-card">
                        <h3>Popular Services</h3>
                        <div id="popular-services" class="loading">Loading...</div>
                    </div>
                    <div class="analytics-card">
                        <h3>Service Revenue</h3>
                        <div id="service-revenue" class="loading">Loading...</div>
                    </div>
                </div>
            </div>

            <!-- Customer Insights -->
            <div class="analytics-section">
                <h2>👥 Customer Insights</h2>
                <div class="analytics-grid">
                    <div class="analytics-card">
                        <h3>Top Locations</h3>
                        <div id="top-locations" class="loading">Loading...</div>
                    </div>
                    <div class="analytics-card">
                        <h3>Booking Patterns</h3>
                        <div id="booking-patterns" class="loading">Loading...</div>
                    </div>
                </div>
            </div>

            <!-- Review Analytics -->
            <div class="analytics-section">
                <h2>⭐ Review Analytics</h2>
                <div class="analytics-grid">
                    <div class="analytics-card">
                        <h3>Rating Distribution</h3>
                        <div id="rating-distribution" class="loading">Loading...</div>
                    </div>
                    <div class="analytics-card">
                        <h3>Review Trends</h3>
                        <div id="review-trends" class="loading">Loading...</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Add Test Data Page -->
        <div id="add-data-page" class="admin-page">
            <div class="page-header">
                <h1>➕ Add Test Data</h1>
                <p>Add sample data to test the system</p>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <!-- Add Inquiry Form -->
                <div class="data-table">
                    <div class="table-header">
                        <h2>Add Test Inquiry</h2>
                    </div>
                    <div class="table-content" style="padding: 20px;">
                        <form id="add-inquiry-form">
                            <div class="form-group">
                                <label class="form-label">Name</label>
                                <input type="text" class="form-input" name="name" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-input" name="email" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Phone</label>
                                <input type="tel" class="form-input" name="phone">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Subject</label>
                                <input type="text" class="form-input" name="subject" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Message</label>
                                <textarea class="form-textarea" name="message" required></textarea>
                            </div>
                            <button type="submit" class="btn btn-success">Add Inquiry</button>
                        </form>
                    </div>
                </div>

                <!-- Add Service Form -->
                <div class="data-table">
                    <div class="table-header">
                        <h2>Add Test Service</h2>
                    </div>
                    <div class="table-content" style="padding: 20px;">
                        <form id="add-service-form">
                            <div class="form-group">
                                <label class="form-label">Service Name</label>
                                <input type="text" class="form-input" name="serviceName" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Service Type</label>
                                <select class="form-select" name="serviceType" required>
                                    <option value="">Select Type</option>
                                    <option value="virtual">Virtual</option>
                                    <option value="site-visit">Site Visit</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Price (₹)</label>
                                <input type="number" class="form-input" name="price" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Short Description</label>
                                <textarea class="form-textarea" name="shortDescription" required></textarea>
                            </div>
                            <button type="submit" class="btn btn-success">Add Service</button>
                        </form>
                    </div>
                </div>

                <!-- Add Review Form -->
                <div class="data-table">
                    <div class="table-header">
                        <h2>Add Test Review</h2>
                    </div>
                    <div class="table-content" style="padding: 20px;">
                        <form id="add-review-form">
                            <div class="form-group">
                                <label class="form-label">Customer Name</label>
                                <input type="text" class="form-input" name="customerName" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Customer Email</label>
                                <input type="email" class="form-input" name="customerEmail" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Service Name</label>
                                <input type="text" class="form-input" name="serviceName" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Service Type</label>
                                <select class="form-select" name="serviceType" required>
                                    <option value="">Select Type</option>
                                    <option value="virtual">Virtual</option>
                                    <option value="site-visit">Site Visit</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Rating</label>
                                <select class="form-select" name="rating" required>
                                    <option value="">Select Rating</option>
                                    <option value="5">5 Stars</option>
                                    <option value="4">4 Stars</option>
                                    <option value="3">3 Stars</option>
                                    <option value="2">2 Stars</option>
                                    <option value="1">1 Star</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Review Title</label>
                                <input type="text" class="form-input" name="title">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Review Text</label>
                                <textarea class="form-textarea" name="reviewText" required></textarea>
                            </div>
                            <button type="submit" class="btn btn-success">Add Review</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // API Configuration
        const API_BASE_URL = 'http://localhost:3000/api';

        // Global state
        let currentData = {
            bookings: [],
            inquiries: [],
            services: [],
            reviews: [],
            analytics: {},
            stats: {},
            selectedReviews: new Set()
        };

        // Utility Functions
        function showError(message) {
            console.error('Error:', message);
            // You could show a toast notification here
        }

        function showSuccess(message) {
            console.log('Success:', message);
            // You could show a toast notification here
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('en-IN', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', {
                style: 'currency',
                currency: 'INR'
            }).format(amount);
        }

        // API Functions
        async function apiCall(endpoint, options = {}) {
            try {
                // Automatically add demo token for admin operations
                const defaultHeaders = {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer demo-token' // Demo token for full admin access
                };

                const response = await fetch(`${API_BASE_URL}${endpoint}`, {
                    headers: {
                        ...defaultHeaders,
                        ...options.headers
                    },
                    ...options
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                // Update connection status
                document.getElementById('connection-status').textContent = '🟢';
                document.getElementById('api-status').textContent = '✅';

                return data;
            } catch (error) {
                console.error('API Error:', error);

                // Update connection status
                document.getElementById('connection-status').textContent = '🔴';
                document.getElementById('api-status').textContent = '❌';

                throw error;
            }
        }

        // Data Loading Functions
        async function loadBookings() {
            try {
                const response = await apiCall('/bookings');
                currentData.bookings = response.data?.bookings || [];
                return currentData.bookings;
            } catch (error) {
                showError('Failed to load bookings');
                return [];
            }
        }

        async function loadInquiries() {
            try {
                console.log('🔄 Loading inquiries...');
                const response = await apiCall('/inquiries');
                console.log('📊 API Response:', response);
                currentData.inquiries = response.data?.inquiries || [];
                console.log(`✅ Loaded ${currentData.inquiries.length} inquiries`);
                return currentData.inquiries;
            } catch (error) {
                console.error('❌ Failed to load inquiries:', error);
                showError('Failed to load inquiries: ' + error.message);
                return [];
            }
        }

        async function loadServices() {
            try {
                const response = await apiCall('/services');
                currentData.services = response.data?.services || [];
                return currentData.services;
            } catch (error) {
                showError('Failed to load services');
                return [];
            }
        }

        async function loadReviews(filters = {}) {
            try {
                const params = new URLSearchParams(filters);
                const response = await apiCall(`/reviews?${params}`);
                currentData.reviews = response.data?.reviews || [];
                return currentData.reviews;
            } catch (error) {
                showError('Failed to load reviews');
                return [];
            }
        }

        async function loadReviewStats() {
            try {
                const response = await apiCall('/reviews/stats/summary');
                return response.data || {};
            } catch (error) {
                showError('Failed to load review statistics');
                return {};
            }
        }

        async function loadAnalytics(period = '30d') {
            try {
                const response = await apiCall(`/analytics/dashboard?period=${period}`);
                currentData.analytics = response.data || {};
                return currentData.analytics;
            } catch (error) {
                showError('Failed to load analytics');
                return {};
            }
        }

        // Dashboard Functions
        async function loadDashboardStats() {
            try {
                // Load all data
                const [bookings, inquiries, services] = await Promise.all([
                    loadBookings(),
                    loadInquiries(),
                    loadServices()
                ]);

                // Update stats
                document.getElementById('total-bookings').textContent = bookings.length;
                document.getElementById('total-inquiries').textContent = inquiries.length;
                document.getElementById('total-services').textContent = services.filter(s => s.isActive).length;

                // Update recent activity
                updateRecentActivity(bookings, inquiries);

            } catch (error) {
                showError('Failed to load dashboard stats');
            }
        }

        function updateRecentActivity(bookings, inquiries) {
            const recentActivityDiv = document.getElementById('recent-activity');

            // Combine and sort recent items
            const recentItems = [
                ...bookings.slice(0, 5).map(b => ({
                    type: 'booking',
                    title: `Booking: ${b.serviceType || 'Service'}`,
                    subtitle: `${b.customerName} - ${b.customerEmail}`,
                    time: b.createdAt || b.bookingDate,
                    status: b.status
                })),
                ...inquiries.slice(0, 5).map(i => ({
                    type: 'inquiry',
                    title: `Inquiry: ${i.subject}`,
                    subtitle: `${i.name} - ${i.email}`,
                    time: i.submittedAt || i.createdAt,
                    status: i.status
                }))
            ].sort((a, b) => new Date(b.time) - new Date(a.time)).slice(0, 10);

            if (recentItems.length === 0) {
                recentActivityDiv.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">No recent activity found. Add some test data to see it here!</div>';
                return;
            }

            const html = `
                <table>
                    <thead>
                        <tr>
                            <th>Type</th>
                            <th>Details</th>
                            <th>Customer</th>
                            <th>Time</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${recentItems.map(item => `
                            <tr>
                                <td>${item.type === 'booking' ? '📅' : '💬'} ${item.type}</td>
                                <td>${item.title}</td>
                                <td>${item.subtitle}</td>
                                <td>${formatDate(item.time)}</td>
                                <td><span class="status-badge status-${item.status || 'pending'}">${item.status || 'pending'}</span></td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            recentActivityDiv.innerHTML = html;
        }

        // Page-specific functions
        function renderBookings(bookings) {
            const contentDiv = document.getElementById('bookings-content');

            if (bookings.length === 0) {
                contentDiv.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">No bookings found. Add some test data!</div>';
                return;
            }

            const html = `
                <table>
                    <thead>
                        <tr>
                            <th>Booking ID</th>
                            <th>Customer</th>
                            <th>Service</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${bookings.map(booking => `
                            <tr>
                                <td>${booking.bookingId || booking._id}</td>
                                <td>
                                    <strong>${booking.customerName}</strong><br>
                                    <small>${booking.customerEmail}</small>
                                </td>
                                <td>${booking.serviceType || 'N/A'}</td>
                                <td>${formatDate(booking.bookingDate || booking.createdAt)}</td>
                                <td><span class="status-badge status-${booking.status || 'pending'}">${booking.status || 'pending'}</span></td>
                                <td>
                                    <button class="btn btn-secondary" onclick="viewBooking('${booking._id}')">View</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            contentDiv.innerHTML = html;
        }

        function renderInquiries(inquiries) {
            console.log(`🎨 Rendering inquiries:`, inquiries);
            const contentDiv = document.getElementById('inquiries-content');
            console.log('📍 Content div found:', !!contentDiv);

            if (inquiries.length === 0) {
                console.log('📭 No inquiries to display');
                contentDiv.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">No inquiries found. Add some test data!</div>';
                return;
            }

            console.log('🏗️ Building HTML table for inquiries...');
            const html = `
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Customer</th>
                            <th>Subject</th>
                            <th>Type</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${inquiries.map(inquiry => `
                            <tr>
                                <td>${inquiry.inquiryId || inquiry._id}</td>
                                <td>
                                    <strong>${inquiry.name}</strong><br>
                                    <small>${inquiry.email || inquiry.phone || 'No contact info'}</small>
                                </td>
                                <td>${inquiry.subject}</td>
                                <td>${inquiry.inquiryType || 'general'}</td>
                                <td>${formatDate(inquiry.submittedAt || inquiry.createdAt)}</td>
                                <td><span class="status-badge status-${inquiry.status || 'unread'}">${inquiry.status || 'unread'}</span></td>
                                <td>
                                    <button class="btn btn-secondary" onclick="viewInquiry('${inquiry._id}')">View</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            console.log('📝 HTML generated, length:', html.length);
            console.log('🎯 Setting innerHTML...');
            contentDiv.innerHTML = html;
            console.log('✅ innerHTML set successfully');
        }

        function renderServices(services) {
            const contentDiv = document.getElementById('services-content');

            if (services.length === 0) {
                contentDiv.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">No services found. Add some test data!</div>';
                return;
            }

            const html = `
                <table>
                    <thead>
                        <tr>
                            <th>Service Name</th>
                            <th>Type</th>
                            <th>Price</th>
                            <th>Description</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${services.map(service => `
                            <tr>
                                <td><strong>${service.serviceName}</strong></td>
                                <td>${service.serviceType}</td>
                                <td>${formatCurrency(service.price)}</td>
                                <td>${service.shortDescription}</td>
                                <td><span class="status-badge ${service.isActive ? 'status-confirmed' : 'status-pending'}">${service.isActive ? 'Active' : 'Inactive'}</span></td>
                                <td>
                                    <button class="btn btn-secondary" onclick="editService('${service._id}')">Edit</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            contentDiv.innerHTML = html;
        }

        function renderReviews(reviews) {
            const contentDiv = document.getElementById('reviews-content');

            if (reviews.length === 0) {
                contentDiv.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">No reviews found. Add some test data!</div>';
                return;
            }

            const html = `
                <table>
                    <thead>
                        <tr>
                            <th><input type="checkbox" onchange="toggleSelectAllReviews()"></th>
                            <th>Customer</th>
                            <th>Service</th>
                            <th>Rating</th>
                            <th>Review</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${reviews.map(review => `
                            <tr>
                                <td><input type="checkbox" class="review-checkbox" value="${review._id}" onchange="updateBulkActions()"></td>
                                <td>
                                    <strong>${review.customerName}</strong><br>
                                    <small>${review.customerEmail}</small>
                                </td>
                                <td>${review.serviceName} (${review.serviceType})</td>
                                <td>
                                    <span class="rating-stars">${'★'.repeat(review.rating)}${'☆'.repeat(5-review.rating)}</span>
                                    <small>(${review.rating}/5)</small>
                                </td>
                                <td>
                                    <strong>${review.title || 'No title'}</strong><br>
                                    <small>${review.reviewText.substring(0, 100)}${review.reviewText.length > 100 ? '...' : ''}</small>
                                </td>
                                <td>${formatDate(review.submittedAt)}</td>
                                <td><span class="status-badge status-${review.status}">${review.status}</span></td>
                                <td>
                                    <button class="btn btn-secondary" onclick="viewReview('${review._id}')">View</button>
                                    ${review.status === 'pending' ? `
                                        <button class="btn btn-success" onclick="approveReview('${review._id}')">✅</button>
                                        <button class="btn btn-danger" onclick="rejectReview('${review._id}')">❌</button>
                                    ` : ''}
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            contentDiv.innerHTML = html;
        }

        function renderReviewStats(stats) {
            document.getElementById('total-reviews').textContent = stats.summary?.total || 0;
            document.getElementById('pending-reviews').textContent = stats.summary?.pending || 0;
            document.getElementById('approved-reviews').textContent = stats.summary?.approved || 0;
            document.getElementById('average-rating').textContent = stats.summary?.averageRating || '0.0';
        }

        function renderAnalytics(analytics) {
            // Update key metrics
            if (analytics.revenue) {
                document.getElementById('total-revenue').textContent = formatCurrency(analytics.revenue.summary?.totalRevenue || 0);
                document.getElementById('analytics-bookings').textContent = analytics.bookings?.total || 0;
            }

            if (analytics.conversions) {
                document.getElementById('conversion-rate').textContent = analytics.conversions.inquiryToBookingRate + '%';
            }

            if (analytics.reviews) {
                document.getElementById('analytics-rating').textContent = analytics.reviews.averageRating || '0.0';
            }

            // Render service performance
            renderServicePerformance(analytics.services);

            // Render customer insights
            renderCustomerInsights(analytics);

            // Render review analytics
            renderReviewAnalytics(analytics.reviews);

            // Render charts
            renderRevenueChart(analytics.revenue);
        }

        function renderRevenueChart(revenueData) {
            const ctx = document.getElementById('revenue-chart');
            if (!ctx) return;

            // Destroy existing chart if it exists
            if (window.revenueChart) {
                window.revenueChart.destroy();
            }

            // Sample data for demonstration
            const chartData = {
                labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                datasets: [{
                    label: 'Revenue (₹)',
                    data: [12000, 19000, 15000, 25000],
                    borderColor: '#2c5aa0',
                    backgroundColor: 'rgba(44, 90, 160, 0.1)',
                    tension: 0.4
                }, {
                    label: 'Bookings',
                    data: [8, 12, 10, 16],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4,
                    yAxisID: 'y1'
                }]
            };

            const config = {
                type: 'line',
                data: chartData,
                options: {
                    responsive: true,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            };

            window.revenueChart = new Chart(ctx, config);
        }

        function renderServicePerformance(serviceData) {
            if (!serviceData) return;

            const popularServicesDiv = document.getElementById('popular-services');
            const serviceRevenueDiv = document.getElementById('service-revenue');

            if (serviceData.popularServices && serviceData.popularServices.length > 0) {
                const popularHtml = serviceData.popularServices.slice(0, 5).map(service => `
                    <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                        <span>${service._id}</span>
                        <span><strong>${service.bookingCount} bookings</strong></span>
                    </div>
                `).join('');
                popularServicesDiv.innerHTML = popularHtml;
            } else {
                popularServicesDiv.innerHTML = '<p>No service data available</p>';
            }

            if (serviceData.popularServices && serviceData.popularServices.length > 0) {
                const revenueHtml = serviceData.popularServices.slice(0, 5).map(service => `
                    <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                        <span>${service._id}</span>
                        <span><strong>${formatCurrency(service.totalRevenue || 0)}</strong></span>
                    </div>
                `).join('');
                serviceRevenueDiv.innerHTML = revenueHtml;
            } else {
                serviceRevenueDiv.innerHTML = '<p>No revenue data available</p>';
            }
        }

        function renderCustomerInsights(analytics) {
            const topLocationsDiv = document.getElementById('top-locations');
            const bookingPatternsDiv = document.getElementById('booking-patterns');

            // Placeholder content for customer insights
            topLocationsDiv.innerHTML = '<p>Customer location data will be displayed here</p>';
            bookingPatternsDiv.innerHTML = '<p>Booking pattern analysis will be displayed here</p>';
        }

        function renderReviewAnalytics(reviewData) {
            const ratingDistributionDiv = document.getElementById('rating-distribution');
            const reviewTrendsDiv = document.getElementById('review-trends');

            if (reviewData && reviewData.ratingDistribution) {
                const distributionHtml = reviewData.ratingDistribution.map(rating => `
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span>${rating._id} Stars</span>
                        <span><strong>${rating.count}</strong></span>
                    </div>
                `).join('');
                ratingDistributionDiv.innerHTML = distributionHtml;
            } else {
                ratingDistributionDiv.innerHTML = '<p>No rating data available</p>';
            }

            reviewTrendsDiv.innerHTML = '<p>Review trends will be displayed here</p>';
        }

        // Form submission functions
        async function submitInquiry(formData) {
            try {
                const response = await apiCall('/inquiries', {
                    method: 'POST',
                    body: JSON.stringify(formData)
                });

                showSuccess('Inquiry added successfully!');
                refreshInquiries();
                refreshDashboard();

                return response;
            } catch (error) {
                showError('Failed to add inquiry: ' + error.message);
                throw error;
            }
        }

        async function submitService(formData) {
            try {
                const response = await apiCall('/services', {
                    method: 'POST',
                    body: JSON.stringify(formData),
                    headers: {
                        'Authorization': 'Bearer demo-token' // Demo token for full access
                    }
                });

                showSuccess('Service added successfully!');
                refreshServices();
                refreshDashboard();

                return response;
            } catch (error) {
                showError('Failed to add service: ' + error.message);
                throw error;
            }
        }

        async function submitReview(formData) {
            try {
                const response = await apiCall('/reviews', {
                    method: 'POST',
                    body: JSON.stringify(formData)
                });

                showSuccess('Review added successfully!');
                refreshReviews();
                refreshDashboard();

                return response;
            } catch (error) {
                showError('Failed to add review: ' + error.message);
                throw error;
            }
        }

        // Refresh functions
        async function refreshDashboard() {
            await loadDashboardStats();
        }

        async function refreshBookings() {
            const bookings = await loadBookings();
            renderBookings(bookings);
        }

        async function refreshInquiries() {
            console.log('🔄 Refreshing inquiries...');
            const inquiries = await loadInquiries();
            console.log(`📋 Rendering ${inquiries.length} inquiries`);
            renderInquiries(inquiries);
        }

        async function refreshServices() {
            const services = await loadServices();
            renderServices(services);
        }

        async function refreshReviews() {
            const [reviews, stats] = await Promise.all([
                loadReviews(),
                loadReviewStats()
            ]);
            renderReviews(reviews);
            renderReviewStats(stats);
        }

        async function refreshAnalytics() {
            const period = document.getElementById('analytics-period')?.value || '30d';
            const analytics = await loadAnalytics(period);
            renderAnalytics(analytics);
        }

        // Navigation
        function showPage(pageId) {
            console.log(`🧭 Navigating to page: ${pageId}`);

            // Hide all pages
            document.querySelectorAll('.admin-page').forEach(page => {
                page.classList.remove('active');
            });

            // Show selected page
            const targetPage = document.getElementById(pageId + '-page');
            console.log(`📄 Target page element found:`, !!targetPage);
            targetPage.classList.add('active');

            // Update navigation
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            document.querySelector(`[data-page="${pageId}"]`).classList.add('active');

            // Load page data
            console.log(`📊 Loading data for page: ${pageId}`);
            switch(pageId) {
                case 'dashboard':
                    refreshDashboard();
                    break;
                case 'bookings':
                    refreshBookings();
                    break;
                case 'inquiries':
                    console.log('🎯 Calling refreshInquiries()');
                    refreshInquiries();
                    break;
                case 'services':
                    refreshServices();
                    break;
                case 'reviews':
                    refreshReviews();
                    break;
                case 'analytics':
                    refreshAnalytics();
                    break;
            }
        }

        // Event Listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Navigation
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const pageId = this.getAttribute('data-page');
                    showPage(pageId);
                });
            });

            // Form submissions
            document.getElementById('add-inquiry-form').addEventListener('submit', async function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const data = Object.fromEntries(formData.entries());

                try {
                    await submitInquiry(data);
                    this.reset();
                } catch (error) {
                    // Error already handled in submitInquiry
                }
            });

            document.getElementById('add-service-form').addEventListener('submit', async function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const data = Object.fromEntries(formData.entries());
                data.price = parseFloat(data.price);

                try {
                    await submitService(data);
                    this.reset();
                } catch (error) {
                    // Error already handled in submitService
                }
            });

            document.getElementById('add-review-form').addEventListener('submit', async function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const data = Object.fromEntries(formData.entries());
                data.rating = parseInt(data.rating);

                try {
                    await submitReview(data);
                    this.reset();
                } catch (error) {
                    // Error already handled in submitReview
                }
            });

            // Initial load
            refreshDashboard();
        });

        // Reviews Management Functions
        async function approveReview(reviewId) {
            try {
                await apiCall(`/reviews/${reviewId}/status`, {
                    method: 'PUT',
                    body: JSON.stringify({ status: 'approved' })
                });
                showSuccess('Review approved successfully!');
                refreshReviews();
            } catch (error) {
                showError('Failed to approve review: ' + error.message);
            }
        }

        async function rejectReview(reviewId) {
            const notes = prompt('Rejection reason (optional):');
            try {
                await apiCall(`/reviews/${reviewId}/status`, {
                    method: 'PUT',
                    body: JSON.stringify({
                        status: 'rejected',
                        moderationNotes: notes || ''
                    })
                });
                showSuccess('Review rejected successfully!');
                refreshReviews();
            } catch (error) {
                showError('Failed to reject review: ' + error.message);
            }
        }

        async function bulkApproveReviews() {
            const selectedIds = Array.from(currentData.selectedReviews);
            if (selectedIds.length === 0) {
                alert('Please select reviews to approve');
                return;
            }

            try {
                await apiCall('/reviews/bulk/status', {
                    method: 'PUT',
                    body: JSON.stringify({
                        reviewIds: selectedIds,
                        status: 'approved'
                    })
                });
                showSuccess(`${selectedIds.length} reviews approved successfully!`);
                currentData.selectedReviews.clear();
                refreshReviews();
            } catch (error) {
                showError('Failed to bulk approve reviews: ' + error.message);
            }
        }

        async function bulkRejectReviews() {
            const selectedIds = Array.from(currentData.selectedReviews);
            if (selectedIds.length === 0) {
                alert('Please select reviews to reject');
                return;
            }

            const notes = prompt('Rejection reason (optional):');
            try {
                await apiCall('/reviews/bulk/status', {
                    method: 'PUT',
                    body: JSON.stringify({
                        reviewIds: selectedIds,
                        status: 'rejected',
                        moderationNotes: notes || ''
                    })
                });
                showSuccess(`${selectedIds.length} reviews rejected successfully!`);
                currentData.selectedReviews.clear();
                refreshReviews();
            } catch (error) {
                showError('Failed to bulk reject reviews: ' + error.message);
            }
        }

        function toggleSelectAllReviews() {
            const selectAll = document.getElementById('select-all-reviews');
            const checkboxes = document.querySelectorAll('.review-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
                if (selectAll.checked) {
                    currentData.selectedReviews.add(checkbox.value);
                } else {
                    currentData.selectedReviews.delete(checkbox.value);
                }
            });

            updateBulkActions();
        }

        function updateBulkActions() {
            const checkboxes = document.querySelectorAll('.review-checkbox:checked');
            currentData.selectedReviews.clear();

            checkboxes.forEach(checkbox => {
                currentData.selectedReviews.add(checkbox.value);
            });

            const bulkApproveBtn = document.getElementById('bulk-approve-btn');
            const bulkRejectBtn = document.getElementById('bulk-reject-btn');

            if (bulkApproveBtn && bulkRejectBtn) {
                const hasSelection = currentData.selectedReviews.size > 0;
                bulkApproveBtn.disabled = !hasSelection;
                bulkRejectBtn.disabled = !hasSelection;
            }
        }

        function filterReviews() {
            const status = document.getElementById('review-status-filter').value;
            const rating = document.getElementById('review-rating-filter').value;
            const serviceType = document.getElementById('review-service-filter').value;

            const filters = {};
            if (status) filters.status = status;
            if (rating) filters.rating = rating;
            if (serviceType) filters.serviceType = serviceType;

            loadReviews(filters).then(reviews => {
                renderReviews(reviews);
            });
        }

        function searchReviews() {
            const searchTerm = document.getElementById('review-search').value;
            const filters = {};
            if (searchTerm) filters.search = searchTerm;

            loadReviews(filters).then(reviews => {
                renderReviews(reviews);
            });
        }

        // Analytics Functions
        function changeAnalyticsPeriod() {
            refreshAnalytics();
        }

        // Placeholder functions for future implementation
        function viewBooking(id) {
            alert('View booking: ' + id + '\n(Feature coming soon)');
        }

        function viewInquiry(id) {
            alert('View inquiry: ' + id + '\n(Feature coming soon)');
        }

        function viewReview(id) {
            alert('View review: ' + id + '\n(Feature coming soon)');
        }

        function editService(id) {
            alert('Edit service: ' + id + '\n(Feature coming soon)');
        }
    </script>
</body>
</html>