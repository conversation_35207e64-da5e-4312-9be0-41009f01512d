<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Builder Ballery - Live Admin Panel</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f1f1f1;
            color: #333;
        }

        .admin-header {
            background: #2c5aa0;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .admin-logo {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .admin-user {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .admin-sidebar {
            position: fixed;
            left: 0;
            top: 70px;
            width: 250px;
            height: calc(100vh - 70px);
            background: white;
            box-shadow: 2px 0 4px rgba(0,0,0,0.1);
            overflow-y: auto;
        }

        .admin-nav {
            list-style: none;
            padding: 20px 0;
        }

        .admin-nav li {
            margin: 0;
        }

        .admin-nav a {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 20px;
            color: #333;
            text-decoration: none;
            transition: background-color 0.2s ease;
        }

        .admin-nav a:hover,
        .admin-nav a.active {
            background: #f8f9fa;
            color: #2c5aa0;
        }

        .admin-content {
            margin-left: 250px;
            padding: 20px;
            min-height: calc(100vh - 70px);
        }

        .page-header {
            background: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-header h1 {
            color: #2c5aa0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #2c5aa0;
            color: white;
        }

        .btn-primary:hover {
            background: #1e3f73;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 15px;
            transition: transform 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-icon {
            font-size: 2.5rem;
            opacity: 0.8;
        }

        .stat-content h3 {
            margin: 0;
            font-size: 2rem;
            font-weight: bold;
            color: #2c5aa0;
        }

        .stat-content p {
            margin: 5px 0;
            font-weight: 600;
            color: #333;
        }

        .data-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-content {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pending { background: #fff3cd; color: #856404; }
        .status-confirmed { background: #d4edda; color: #155724; }
        .status-completed { background: #cce5ff; color: #004085; }
        .status-unread { background: #fff3cd; color: #856404; }
        .status-read { background: #d4edda; color: #155724; }
        .status-responded { background: #cce5ff; color: #004085; }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .form-input,
        .form-textarea,
        .form-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #999;
        }

        .close:hover {
            color: #333;
        }

        .admin-page {
            display: none;
        }

        .admin-page.active {
            display: block;
        }

        @media (max-width: 768px) {
            .admin-sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .admin-content {
                margin-left: 0;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Admin Header -->
    <header class="admin-header">
        <div class="admin-logo">
            🏗️ BUILDER BALLERY - Live Admin Panel
        </div>
        <div class="admin-user">
            <span>🔑 Demo Token Active</span>
            <span id="connection-status">🟢</span>
        </div>
    </header>

    <!-- Admin Sidebar -->
    <nav class="admin-sidebar">
        <ul class="admin-nav">
            <li><a href="#dashboard" class="nav-link active" data-page="dashboard">📊 Dashboard</a></li>
            <li><a href="#bookings" class="nav-link" data-page="bookings">📅 Bookings</a></li>
            <li><a href="#inquiries" class="nav-link" data-page="inquiries">💬 Inquiries</a></li>
            <li><a href="#services" class="nav-link" data-page="services">🛠️ Services</a></li>
            <li><a href="#add-data" class="nav-link" data-page="add-data">➕ Add Test Data</a></li>
        </ul>
    </nav>

    <!-- Main Content -->
    <main class="admin-content">
        <!-- Dashboard Page -->
        <div id="dashboard-page" class="admin-page active">
            <div class="page-header">
                <h1>📊 Live Dashboard</h1>
                <button class="btn btn-primary" onclick="refreshDashboard()">🔄 Refresh Data</button>
            </div>

            <!-- Statistics Grid -->
            <div class="stats-grid" id="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">📅</div>
                    <div class="stat-content">
                        <h3 id="total-bookings">-</h3>
                        <p>Total Bookings</p>
                        <small>Loading...</small>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">💬</div>
                    <div class="stat-content">
                        <h3 id="total-inquiries">-</h3>
                        <p>Total Inquiries</p>
                        <small>Loading...</small>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">🛠️</div>
                    <div class="stat-content">
                        <h3 id="total-services">-</h3>
                        <p>Active Services</p>
                        <small>Loading...</small>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">🔗</div>
                    <div class="stat-content">
                        <h3 id="api-status">✅</h3>
                        <p>API Status</p>
                        <small>Connected</small>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="data-table">
                <div class="table-header">
                    <h2>Recent Activity</h2>
                </div>
                <div class="table-content">
                    <div id="recent-activity" class="loading">Loading recent activity...</div>
                </div>
            </div>
        </div>

        <!-- Bookings Page -->
        <div id="bookings-page" class="admin-page">
            <div class="page-header">
                <h1>📅 Bookings Management</h1>
                <button class="btn btn-primary" onclick="refreshBookings()">🔄 Refresh</button>
            </div>

            <div class="data-table">
                <div class="table-header">
                    <h2>All Bookings</h2>
                </div>
                <div class="table-content">
                    <div id="bookings-content" class="loading">Loading bookings...</div>
                </div>
            </div>
        </div>

        <!-- Inquiries Page -->
        <div id="inquiries-page" class="admin-page">
            <div class="page-header">
                <h1>💬 Customer Inquiries</h1>
                <button class="btn btn-primary" onclick="refreshInquiries()">🔄 Refresh</button>
            </div>

            <div class="data-table">
                <div class="table-header">
                    <h2>All Inquiries</h2>
                </div>
                <div class="table-content">
                    <div id="inquiries-content" class="loading">Loading inquiries...</div>
                </div>
            </div>
        </div>

        <!-- Services Page -->
        <div id="services-page" class="admin-page">
            <div class="page-header">
                <h1>🛠️ Services Management</h1>
                <button class="btn btn-primary" onclick="refreshServices()">🔄 Refresh</button>
            </div>

            <div class="data-table">
                <div class="table-header">
                    <h2>All Services</h2>
                </div>
                <div class="table-content">
                    <div id="services-content" class="loading">Loading services...</div>
                </div>
            </div>
        </div>

        <!-- Add Test Data Page -->
        <div id="add-data-page" class="admin-page">
            <div class="page-header">
                <h1>➕ Add Test Data</h1>
                <p>Add sample data to test the system</p>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <!-- Add Inquiry Form -->
                <div class="data-table">
                    <div class="table-header">
                        <h2>Add Test Inquiry</h2>
                    </div>
                    <div class="table-content" style="padding: 20px;">
                        <form id="add-inquiry-form">
                            <div class="form-group">
                                <label class="form-label">Name</label>
                                <input type="text" class="form-input" name="name" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-input" name="email" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Phone</label>
                                <input type="tel" class="form-input" name="phone">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Subject</label>
                                <input type="text" class="form-input" name="subject" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Message</label>
                                <textarea class="form-textarea" name="message" required></textarea>
                            </div>
                            <button type="submit" class="btn btn-success">Add Inquiry</button>
                        </form>
                    </div>
                </div>

                <!-- Add Service Form -->
                <div class="data-table">
                    <div class="table-header">
                        <h2>Add Test Service</h2>
                    </div>
                    <div class="table-content" style="padding: 20px;">
                        <form id="add-service-form">
                            <div class="form-group">
                                <label class="form-label">Service Name</label>
                                <input type="text" class="form-input" name="serviceName" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Service Type</label>
                                <select class="form-select" name="serviceType" required>
                                    <option value="">Select Type</option>
                                    <option value="virtual">Virtual</option>
                                    <option value="site-visit">Site Visit</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Price (₹)</label>
                                <input type="number" class="form-input" name="price" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Short Description</label>
                                <textarea class="form-textarea" name="shortDescription" required></textarea>
                            </div>
                            <button type="submit" class="btn btn-success">Add Service</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // API Configuration
        const API_BASE_URL = 'http://localhost:3000/api';

        // Global state
        let currentData = {
            bookings: [],
            inquiries: [],
            services: [],
            stats: {}
        };

        // Utility Functions
        function showError(message) {
            console.error('Error:', message);
            // You could show a toast notification here
        }

        function showSuccess(message) {
            console.log('Success:', message);
            // You could show a toast notification here
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('en-IN', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', {
                style: 'currency',
                currency: 'INR'
            }).format(amount);
        }

        // API Functions
        async function apiCall(endpoint, options = {}) {
            try {
                // Automatically add demo token for admin operations
                const defaultHeaders = {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer demo-token' // Demo token for full admin access
                };

                const response = await fetch(`${API_BASE_URL}${endpoint}`, {
                    headers: {
                        ...defaultHeaders,
                        ...options.headers
                    },
                    ...options
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                // Update connection status
                document.getElementById('connection-status').textContent = '🟢';
                document.getElementById('api-status').textContent = '✅';

                return data;
            } catch (error) {
                console.error('API Error:', error);

                // Update connection status
                document.getElementById('connection-status').textContent = '🔴';
                document.getElementById('api-status').textContent = '❌';

                throw error;
            }
        }

        // Data Loading Functions
        async function loadBookings() {
            try {
                const response = await apiCall('/bookings');
                currentData.bookings = response.data?.bookings || [];
                return currentData.bookings;
            } catch (error) {
                showError('Failed to load bookings');
                return [];
            }
        }

        async function loadInquiries() {
            try {
                const response = await apiCall('/inquiries');
                currentData.inquiries = response.data?.inquiries || [];
                return currentData.inquiries;
            } catch (error) {
                showError('Failed to load inquiries');
                return [];
            }
        }

        async function loadServices() {
            try {
                const response = await apiCall('/services');
                currentData.services = response.data?.services || [];
                return currentData.services;
            } catch (error) {
                showError('Failed to load services');
                return [];
            }
        }

        // Dashboard Functions
        async function loadDashboardStats() {
            try {
                // Load all data
                const [bookings, inquiries, services] = await Promise.all([
                    loadBookings(),
                    loadInquiries(),
                    loadServices()
                ]);

                // Update stats
                document.getElementById('total-bookings').textContent = bookings.length;
                document.getElementById('total-inquiries').textContent = inquiries.length;
                document.getElementById('total-services').textContent = services.filter(s => s.isActive).length;

                // Update recent activity
                updateRecentActivity(bookings, inquiries);

            } catch (error) {
                showError('Failed to load dashboard stats');
            }
        }

        function updateRecentActivity(bookings, inquiries) {
            const recentActivityDiv = document.getElementById('recent-activity');

            // Combine and sort recent items
            const recentItems = [
                ...bookings.slice(0, 5).map(b => ({
                    type: 'booking',
                    title: `Booking: ${b.serviceType || 'Service'}`,
                    subtitle: `${b.customerName} - ${b.customerEmail}`,
                    time: b.createdAt || b.bookingDate,
                    status: b.status
                })),
                ...inquiries.slice(0, 5).map(i => ({
                    type: 'inquiry',
                    title: `Inquiry: ${i.subject}`,
                    subtitle: `${i.name} - ${i.email}`,
                    time: i.submittedAt || i.createdAt,
                    status: i.status
                }))
            ].sort((a, b) => new Date(b.time) - new Date(a.time)).slice(0, 10);

            if (recentItems.length === 0) {
                recentActivityDiv.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">No recent activity found. Add some test data to see it here!</div>';
                return;
            }

            const html = `
                <table>
                    <thead>
                        <tr>
                            <th>Type</th>
                            <th>Details</th>
                            <th>Customer</th>
                            <th>Time</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${recentItems.map(item => `
                            <tr>
                                <td>${item.type === 'booking' ? '📅' : '💬'} ${item.type}</td>
                                <td>${item.title}</td>
                                <td>${item.subtitle}</td>
                                <td>${formatDate(item.time)}</td>
                                <td><span class="status-badge status-${item.status || 'pending'}">${item.status || 'pending'}</span></td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            recentActivityDiv.innerHTML = html;
        }

        // Page-specific functions
        function renderBookings(bookings) {
            const contentDiv = document.getElementById('bookings-content');

            if (bookings.length === 0) {
                contentDiv.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">No bookings found. Add some test data!</div>';
                return;
            }

            const html = `
                <table>
                    <thead>
                        <tr>
                            <th>Booking ID</th>
                            <th>Customer</th>
                            <th>Service</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${bookings.map(booking => `
                            <tr>
                                <td>${booking.bookingId || booking._id}</td>
                                <td>
                                    <strong>${booking.customerName}</strong><br>
                                    <small>${booking.customerEmail}</small>
                                </td>
                                <td>${booking.serviceType || 'N/A'}</td>
                                <td>${formatDate(booking.bookingDate || booking.createdAt)}</td>
                                <td><span class="status-badge status-${booking.status || 'pending'}">${booking.status || 'pending'}</span></td>
                                <td>
                                    <button class="btn btn-secondary" onclick="viewBooking('${booking._id}')">View</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            contentDiv.innerHTML = html;
        }

        function renderInquiries(inquiries) {
            const contentDiv = document.getElementById('inquiries-content');

            if (inquiries.length === 0) {
                contentDiv.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">No inquiries found. Add some test data!</div>';
                return;
            }

            const html = `
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Customer</th>
                            <th>Subject</th>
                            <th>Type</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${inquiries.map(inquiry => `
                            <tr>
                                <td>${inquiry.inquiryId || inquiry._id}</td>
                                <td>
                                    <strong>${inquiry.name}</strong><br>
                                    <small>${inquiry.email}</small>
                                </td>
                                <td>${inquiry.subject}</td>
                                <td>${inquiry.inquiryType || 'general'}</td>
                                <td>${formatDate(inquiry.submittedAt || inquiry.createdAt)}</td>
                                <td><span class="status-badge status-${inquiry.status || 'unread'}">${inquiry.status || 'unread'}</span></td>
                                <td>
                                    <button class="btn btn-secondary" onclick="viewInquiry('${inquiry._id}')">View</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            contentDiv.innerHTML = html;
        }

        function renderServices(services) {
            const contentDiv = document.getElementById('services-content');

            if (services.length === 0) {
                contentDiv.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">No services found. Add some test data!</div>';
                return;
            }

            const html = `
                <table>
                    <thead>
                        <tr>
                            <th>Service Name</th>
                            <th>Type</th>
                            <th>Price</th>
                            <th>Description</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${services.map(service => `
                            <tr>
                                <td><strong>${service.serviceName}</strong></td>
                                <td>${service.serviceType}</td>
                                <td>${formatCurrency(service.price)}</td>
                                <td>${service.shortDescription}</td>
                                <td><span class="status-badge ${service.isActive ? 'status-confirmed' : 'status-pending'}">${service.isActive ? 'Active' : 'Inactive'}</span></td>
                                <td>
                                    <button class="btn btn-secondary" onclick="editService('${service._id}')">Edit</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            contentDiv.innerHTML = html;
        }

        // Form submission functions
        async function submitInquiry(formData) {
            try {
                const response = await apiCall('/inquiries', {
                    method: 'POST',
                    body: JSON.stringify(formData)
                });

                showSuccess('Inquiry added successfully!');
                refreshInquiries();
                refreshDashboard();

                return response;
            } catch (error) {
                showError('Failed to add inquiry: ' + error.message);
                throw error;
            }
        }

        async function submitService(formData) {
            try {
                const response = await apiCall('/services', {
                    method: 'POST',
                    body: JSON.stringify(formData),
                    headers: {
                        'Authorization': 'Bearer demo-token' // Demo token for full access
                    }
                });

                showSuccess('Service added successfully!');
                refreshServices();
                refreshDashboard();

                return response;
            } catch (error) {
                showError('Failed to add service: ' + error.message);
                throw error;
            }
        }

        // Refresh functions
        async function refreshDashboard() {
            await loadDashboardStats();
        }

        async function refreshBookings() {
            const bookings = await loadBookings();
            renderBookings(bookings);
        }

        async function refreshInquiries() {
            const inquiries = await loadInquiries();
            renderInquiries(inquiries);
        }

        async function refreshServices() {
            const services = await loadServices();
            renderServices(services);
        }

        // Navigation
        function showPage(pageId) {
            // Hide all pages
            document.querySelectorAll('.admin-page').forEach(page => {
                page.classList.remove('active');
            });

            // Show selected page
            document.getElementById(pageId + '-page').classList.add('active');

            // Update navigation
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            document.querySelector(`[data-page="${pageId}"]`).classList.add('active');

            // Load page data
            switch(pageId) {
                case 'dashboard':
                    refreshDashboard();
                    break;
                case 'bookings':
                    refreshBookings();
                    break;
                case 'inquiries':
                    refreshInquiries();
                    break;
                case 'services':
                    refreshServices();
                    break;
            }
        }

        // Event Listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Navigation
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const pageId = this.getAttribute('data-page');
                    showPage(pageId);
                });
            });

            // Form submissions
            document.getElementById('add-inquiry-form').addEventListener('submit', async function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const data = Object.fromEntries(formData.entries());

                try {
                    await submitInquiry(data);
                    this.reset();
                } catch (error) {
                    // Error already handled in submitInquiry
                }
            });

            document.getElementById('add-service-form').addEventListener('submit', async function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const data = Object.fromEntries(formData.entries());
                data.price = parseFloat(data.price);

                try {
                    await submitService(data);
                    this.reset();
                } catch (error) {
                    // Error already handled in submitService
                }
            });

            // Initial load
            refreshDashboard();
        });

        // Placeholder functions for future implementation
        function viewBooking(id) {
            alert('View booking: ' + id + '\n(Feature coming soon)');
        }

        function viewInquiry(id) {
            alert('View inquiry: ' + id + '\n(Feature coming soon)');
        }

        function editService(id) {
            alert('Edit service: ' + id + '\n(Feature coming soon)');
        }
    </script>
</body>
</html>