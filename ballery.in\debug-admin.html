<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Admin Panel - Builder Ballery</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .debug-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            padding: 10px 20px;
            background-color: #2c5aa0;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #1e3f73;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .log {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔧 Debug Admin Panel - Builder Ballery</h1>
    
    <div class="debug-section">
        <h2>📊 API Connection Test</h2>
        <div id="connection-status">
            <p>Click "Test Connection" to check API connectivity...</p>
        </div>
        <button onclick="testConnection()">🔗 Test Connection</button>
        <button onclick="testInquiriesAPI()">📋 Test Inquiries API</button>
        <button onclick="testAnalyticsAPI()">📈 Test Analytics API</button>
    </div>

    <div class="debug-section">
        <h2>💬 Inquiries Data</h2>
        <div id="inquiries-status">
            <p>Click "Load Inquiries" to fetch data...</p>
        </div>
        <button onclick="loadInquiries()">📥 Load Inquiries</button>
        <button onclick="clearInquiriesDisplay()">🗑️ Clear Display</button>
        
        <div id="inquiries-display"></div>
    </div>

    <div class="debug-section">
        <h2>📈 Analytics Data</h2>
        <div id="analytics-status">
            <p>Click "Load Analytics" to fetch data...</p>
        </div>
        <button onclick="loadAnalytics()">📊 Load Analytics</button>
        
        <div id="analytics-display"></div>
    </div>

    <div class="debug-section">
        <h2>🔍 Debug Log</h2>
        <button onclick="clearLog()">🗑️ Clear Log</button>
        <div id="debug-log" class="log">Debug log will appear here...\n</div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3000/api';
        
        function log(message) {
            const logDiv = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('debug-log').textContent = 'Debug log cleared...\n';
        }

        async function testConnection() {
            const statusDiv = document.getElementById('connection-status');
            log('Testing API connection...');
            
            try {
                statusDiv.innerHTML = '<div class="info">🔄 Testing connection...</div>';
                
                const response = await fetch(`${API_BASE_URL}/health`);
                const data = await response.json();
                
                log(`✅ API Health Check: ${data.status}`);
                log(`⏱️ Uptime: ${Math.round(data.uptime)} seconds`);
                log(`🌍 Environment: ${data.environment}`);
                
                statusDiv.innerHTML = `
                    <div class="success">
                        ✅ API Connection: SUCCESS<br>
                        📊 Status: ${data.status}<br>
                        ⏱️ Uptime: ${Math.round(data.uptime)} seconds<br>
                        🌍 Environment: ${data.environment}
                    </div>
                `;
            } catch (error) {
                log(`❌ Connection failed: ${error.message}`);
                statusDiv.innerHTML = `
                    <div class="error">
                        ❌ API Connection: FAILED<br>
                        Error: ${error.message}<br>
                        Make sure backend is running on port 3000
                    </div>
                `;
            }
        }

        async function testInquiriesAPI() {
            log('Testing Inquiries API...');
            
            try {
                const response = await fetch(`${API_BASE_URL}/inquiries`, {
                    headers: {
                        'Authorization': 'Bearer demo-token'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                log(`✅ Inquiries API: SUCCESS`);
                log(`📊 Total inquiries: ${data.data?.inquiries?.length || 0}`);
                log(`📄 Response structure: ${JSON.stringify(Object.keys(data), null, 2)}`);
                
                if (data.data?.inquiries?.length > 0) {
                    log(`📋 First inquiry: ${JSON.stringify(data.data.inquiries[0], null, 2)}`);
                }
                
            } catch (error) {
                log(`❌ Inquiries API failed: ${error.message}`);
            }
        }

        async function testAnalyticsAPI() {
            log('Testing Analytics API...');
            
            try {
                const response = await fetch(`${API_BASE_URL}/analytics/dashboard`, {
                    headers: {
                        'Authorization': 'Bearer demo-token'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                log(`✅ Analytics API: SUCCESS`);
                log(`📊 Inquiries total: ${data.data?.inquiries?.total || 0}`);
                log(`📈 Analytics structure: ${JSON.stringify(Object.keys(data.data || {}), null, 2)}`);
                
            } catch (error) {
                log(`❌ Analytics API failed: ${error.message}`);
            }
        }

        async function loadInquiries() {
            const statusDiv = document.getElementById('inquiries-status');
            const displayDiv = document.getElementById('inquiries-display');
            
            log('Loading inquiries...');
            
            try {
                statusDiv.innerHTML = '<div class="info">🔄 Loading inquiries...</div>';
                
                const response = await fetch(`${API_BASE_URL}/inquiries`, {
                    headers: {
                        'Authorization': 'Bearer demo-token'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                const inquiries = data.data?.inquiries || [];
                
                log(`✅ Loaded ${inquiries.length} inquiries`);
                
                statusDiv.innerHTML = `
                    <div class="success">
                        ✅ Loaded ${inquiries.length} inquiries successfully
                    </div>
                `;
                
                if (inquiries.length === 0) {
                    displayDiv.innerHTML = '<div class="info">📭 No inquiries found. Submit a form to create test data!</div>';
                    return;
                }
                
                const tableHtml = `
                    <table>
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Subject</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Source</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${inquiries.map(inquiry => `
                                <tr>
                                    <td>${inquiry.inquiryId || inquiry._id}</td>
                                    <td><strong>${inquiry.name}</strong></td>
                                    <td>${inquiry.email || inquiry.phone || 'No contact'}</td>
                                    <td>${inquiry.subject}</td>
                                    <td>${inquiry.inquiryType || 'general'}</td>
                                    <td><span style="background: #e3f2fd; padding: 2px 6px; border-radius: 3px;">${inquiry.status || 'unread'}</span></td>
                                    <td>${inquiry.source}</td>
                                    <td>${new Date(inquiry.createdAt).toLocaleString()}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;
                
                displayDiv.innerHTML = tableHtml;
                
                // Log detailed info about each inquiry
                inquiries.forEach((inquiry, index) => {
                    log(`📋 Inquiry ${index + 1}: ${inquiry.name} - ${inquiry.subject} (${inquiry.status})`);
                });
                
            } catch (error) {
                log(`❌ Failed to load inquiries: ${error.message}`);
                statusDiv.innerHTML = `
                    <div class="error">
                        ❌ Failed to load inquiries<br>
                        Error: ${error.message}
                    </div>
                `;
            }
        }

        async function loadAnalytics() {
            const statusDiv = document.getElementById('analytics-status');
            const displayDiv = document.getElementById('analytics-display');
            
            log('Loading analytics...');
            
            try {
                statusDiv.innerHTML = '<div class="info">🔄 Loading analytics...</div>';
                
                const response = await fetch(`${API_BASE_URL}/analytics/dashboard`, {
                    headers: {
                        'Authorization': 'Bearer demo-token'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                const analytics = data.data || {};
                
                log(`✅ Analytics loaded successfully`);
                
                statusDiv.innerHTML = `
                    <div class="success">
                        ✅ Analytics loaded successfully
                    </div>
                `;
                
                const analyticsHtml = `
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 15px 0;">
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                            <h4>📊 Inquiries</h4>
                            <p><strong>Total:</strong> ${analytics.inquiries?.total || 0}</p>
                            <p><strong>Unread:</strong> ${analytics.inquiries?.statusBreakdown?.find(s => s._id === 'unread')?.count || 0}</p>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                            <h4>📅 Bookings</h4>
                            <p><strong>Total:</strong> ${analytics.bookings?.total || 0}</p>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                            <h4>⭐ Reviews</h4>
                            <p><strong>Total:</strong> ${analytics.reviews?.total || 0}</p>
                            <p><strong>Avg Rating:</strong> ${analytics.reviews?.averageRating || '0.0'}</p>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                            <h4>💰 Revenue</h4>
                            <p><strong>Total:</strong> ₹${analytics.revenue?.summary?.totalRevenue || 0}</p>
                        </div>
                    </div>
                `;
                
                displayDiv.innerHTML = analyticsHtml;
                
                log(`📊 Analytics summary: ${analytics.inquiries?.total || 0} inquiries, ${analytics.bookings?.total || 0} bookings`);
                
            } catch (error) {
                log(`❌ Failed to load analytics: ${error.message}`);
                statusDiv.innerHTML = `
                    <div class="error">
                        ❌ Failed to load analytics<br>
                        Error: ${error.message}
                    </div>
                `;
            }
        }

        function clearInquiriesDisplay() {
            document.getElementById('inquiries-display').innerHTML = '';
            document.getElementById('inquiries-status').innerHTML = '<p>Display cleared. Click "Load Inquiries" to fetch data...</p>';
            log('Inquiries display cleared');
        }

        // Auto-run connection test on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('Debug admin panel loaded');
            testConnection();
        });
    </script>
</body>
</html>
