/**
 * Test auth routes
 */

const express = require('express');
const mongoose = require('mongoose');
require('dotenv').config();

const app = express();

// Basic middleware
app.use(express.json());

// Environment variables
const PORT = 3001;
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/builder_ballery';

console.log('Testing auth routes import...');

try {
    const authRoutes = require('./routes/auth');
    console.log('✅ Auth routes imported successfully');
    
    // Use auth routes
    app.use('/api/auth', authRoutes);
    console.log('✅ Auth routes registered successfully');
    
    // Basic route
    app.get('/', (req, res) => {
        res.json({ message: 'Auth routes test server', status: 'OK' });
    });
    
    // Database connection
    mongoose.connect(MONGODB_URI, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
    })
    .then(() => {
        console.log('✅ Connected to MongoDB');
        
        // Start server
        const server = app.listen(PORT, () => {
            console.log(`🚀 Auth routes test server running on port ${PORT}`);
            console.log(`🔗 Test URL: http://localhost:${PORT}`);
            console.log(`🔗 Auth API: http://localhost:${PORT}/api/auth`);
        });
        
        server.on('error', (error) => {
            if (error.code === 'EADDRINUSE') {
                console.error(`❌ Port ${PORT} is already in use`);
                process.exit(1);
            } else {
                console.error('❌ Server error:', error);
            }
        });
        
    })
    .catch((error) => {
        console.error('❌ MongoDB connection error:', error);
        console.log('⚠️  Server will continue without database connection');
        
        // Start server anyway
        const server = app.listen(PORT, () => {
            console.log(`🚀 Auth routes test server running on port ${PORT} (no DB)`);
            console.log(`🔗 Test URL: http://localhost:${PORT}`);
        });
    });
    
} catch (error) {
    console.error('❌ Error with auth routes:', error);
    console.error('Stack trace:', error.stack);
}
