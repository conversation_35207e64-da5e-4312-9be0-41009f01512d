const mongoose = require('mongoose');

/**
 * Review Schema for Builder Ballery
 * Manages customer reviews and testimonials
 */
const reviewSchema = new mongoose.Schema({
    // Review Identification
    reviewId: {
        type: String,
        unique: true,
        required: true,
        index: true
    },
    
    // Customer Information
    customerName: {
        type: String,
        required: true,
        trim: true,
        maxlength: 100
    },
    
    customerEmail: {
        type: String,
        required: true,
        trim: true,
        lowercase: true,
        match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
    },
    
    customerPhone: {
        type: String,
        trim: true,
        match: [/^[\+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number']
    },
    
    // Service Information
    serviceId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Service',
        index: true
    },
    
    serviceName: {
        type: String,
        required: true,
        trim: true
    },
    
    serviceType: {
        type: String,
        enum: ['virtual', 'site-visit'],
        required: true,
        index: true
    },
    
    // Related Booking (if applicable)
    bookingId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Booking',
        index: true
    },
    
    // Review Content
    rating: {
        type: Number,
        required: true,
        min: 1,
        max: 5,
        index: true
    },
    
    title: {
        type: String,
        trim: true,
        maxlength: 200
    },
    
    reviewText: {
        type: String,
        required: true,
        trim: true,
        maxlength: 2000
    },
    
    // Review Status and Moderation
    status: {
        type: String,
        enum: ['pending', 'approved', 'rejected', 'hidden'],
        default: 'pending',
        index: true
    },
    
    moderationNotes: {
        type: String,
        trim: true,
        maxlength: 500
    },
    
    moderatedBy: {
        type: String,
        trim: true
    },
    
    moderatedAt: Date,
    
    // Display Settings
    isFeatured: {
        type: Boolean,
        default: false,
        index: true
    },
    
    showOnWebsite: {
        type: Boolean,
        default: false,
        index: true
    },
    
    displayOrder: {
        type: Number,
        default: 0
    },
    
    // Additional Information
    projectLocation: {
        type: String,
        trim: true,
        maxlength: 200
    },
    
    projectType: {
        type: String,
        trim: true,
        maxlength: 100
    },
    
    projectValue: {
        type: Number,
        min: 0
    },
    
    // Media Attachments
    images: [{
        filename: String,
        originalName: String,
        path: String,
        url: String,
        size: Number,
        mimetype: String,
        uploadedAt: {
            type: Date,
            default: Date.now
        }
    }],
    
    // Verification
    isVerified: {
        type: Boolean,
        default: false,
        index: true
    },
    
    verificationMethod: {
        type: String,
        enum: ['email', 'phone', 'booking', 'manual'],
        default: 'email'
    },
    
    verifiedAt: Date,
    
    // Response from Business
    businessResponse: {
        text: String,
        respondedBy: String,
        respondedAt: Date
    },
    
    // Analytics and Engagement
    metrics: {
        views: {
            type: Number,
            default: 0
        },
        likes: {
            type: Number,
            default: 0
        },
        shares: {
            type: Number,
            default: 0
        },
        helpfulVotes: {
            type: Number,
            default: 0
        },
        reportCount: {
            type: Number,
            default: 0
        }
    },
    
    // Source and Attribution
    source: {
        type: String,
        enum: ['website', 'google', 'facebook', 'email', 'phone', 'manual'],
        default: 'website'
    },
    
    sourceUrl: String,
    
    // Timestamps
    submittedAt: {
        type: Date,
        default: Date.now,
        index: true
    },
    
    createdAt: {
        type: Date,
        default: Date.now
    },
    
    updatedAt: {
        type: Date,
        default: Date.now
    },
    
    // Soft Delete
    isDeleted: {
        type: Boolean,
        default: false,
        index: true
    },
    
    deletedAt: Date,
    deletedBy: String

}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Indexes for performance
reviewSchema.index({ reviewId: 1 });
reviewSchema.index({ customerEmail: 1 });
reviewSchema.index({ status: 1, submittedAt: -1 });
reviewSchema.index({ rating: 1, status: 1 });
reviewSchema.index({ serviceId: 1, status: 1 });
reviewSchema.index({ isFeatured: 1, showOnWebsite: 1 });
reviewSchema.index({ isDeleted: 1, submittedAt: -1 });

// Virtual fields
reviewSchema.virtual('averageRating').get(function() {
    return this.rating;
});

reviewSchema.virtual('isPositive').get(function() {
    return this.rating >= 4;
});

reviewSchema.virtual('responseTime').get(function() {
    if (this.businessResponse && this.businessResponse.respondedAt && this.submittedAt) {
        return Math.round((this.businessResponse.respondedAt - this.submittedAt) / (1000 * 60 * 60)); // hours
    }
    return null;
});

// Pre-save middleware
reviewSchema.pre('save', function(next) {
    // Generate reviewId if not exists
    if (!this.reviewId) {
        this.reviewId = 'REV-' + Date.now() + '-' + Math.random().toString(36).substr(2, 5).toUpperCase();
    }

    // Update timestamps
    this.updatedAt = new Date();

    // Set moderation timestamp when status changes
    if (this.isModified('status') && this.status !== 'pending' && !this.moderatedAt) {
        this.moderatedAt = new Date();
    }

    // Set verification timestamp
    if (this.isModified('isVerified') && this.isVerified && !this.verifiedAt) {
        this.verifiedAt = new Date();
    }

    // Auto-show approved reviews on website
    if (this.isModified('status') && this.status === 'approved') {
        this.showOnWebsite = true;
    }

    next();
});

// Static methods
reviewSchema.statics.findByReviewId = function(reviewId) {
    return this.findOne({ reviewId, isDeleted: false });
};

reviewSchema.statics.findApproved = function() {
    return this.find({
        status: 'approved',
        isDeleted: false
    }).sort({ submittedAt: -1 });
};

reviewSchema.statics.findPending = function() {
    return this.find({
        status: 'pending',
        isDeleted: false
    }).sort({ submittedAt: 1 });
};

reviewSchema.statics.findFeatured = function() {
    return this.find({
        isFeatured: true,
        status: 'approved',
        showOnWebsite: true,
        isDeleted: false
    }).sort({ displayOrder: 1, submittedAt: -1 });
};

reviewSchema.statics.findByService = function(serviceId) {
    return this.find({
        serviceId,
        status: 'approved',
        isDeleted: false
    }).sort({ submittedAt: -1 });
};

reviewSchema.statics.findByRating = function(rating) {
    return this.find({
        rating,
        status: 'approved',
        isDeleted: false
    }).sort({ submittedAt: -1 });
};

reviewSchema.statics.getReviewStats = function() {
    return this.aggregate([
        { $match: { isDeleted: false } },
        {
            $group: {
                _id: '$status',
                count: { $sum: 1 },
                avgRating: { $avg: '$rating' }
            }
        }
    ]);
};

reviewSchema.statics.getRatingDistribution = function() {
    return this.aggregate([
        { $match: { isDeleted: false, status: 'approved' } },
        {
            $group: {
                _id: '$rating',
                count: { $sum: 1 }
            }
        },
        { $sort: { _id: -1 } }
    ]);
};

reviewSchema.statics.getServiceReviewStats = function() {
    return this.aggregate([
        { $match: { isDeleted: false, status: 'approved' } },
        {
            $group: {
                _id: '$serviceName',
                count: { $sum: 1 },
                avgRating: { $avg: '$rating' },
                totalRating: { $sum: '$rating' }
            }
        },
        { $sort: { count: -1 } }
    ]);
};

// Instance methods
reviewSchema.methods.approve = function(moderatedBy = 'system') {
    this.status = 'approved';
    this.moderatedBy = moderatedBy;
    this.moderatedAt = new Date();
    this.showOnWebsite = true;
    return this.save();
};

reviewSchema.methods.reject = function(moderatedBy = 'system', notes = '') {
    this.status = 'rejected';
    this.moderatedBy = moderatedBy;
    this.moderatedAt = new Date();
    this.moderationNotes = notes;
    this.showOnWebsite = false;
    return this.save();
};

reviewSchema.methods.hide = function(moderatedBy = 'system', notes = '') {
    this.status = 'hidden';
    this.moderatedBy = moderatedBy;
    this.moderatedAt = new Date();
    this.moderationNotes = notes;
    this.showOnWebsite = false;
    return this.save();
};

reviewSchema.methods.addBusinessResponse = function(responseText, respondedBy = 'system') {
    this.businessResponse = {
        text: responseText,
        respondedBy: respondedBy,
        respondedAt: new Date()
    };
    return this.save();
};

reviewSchema.methods.incrementViews = function() {
    this.metrics.views += 1;
    return this.save();
};

reviewSchema.methods.toggleFeatured = function() {
    this.isFeatured = !this.isFeatured;
    return this.save();
};

const Review = mongoose.model('Review', reviewSchema);

module.exports = Review;
