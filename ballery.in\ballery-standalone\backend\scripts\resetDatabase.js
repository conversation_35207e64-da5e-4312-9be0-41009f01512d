/**
 * Database Reset Script for Builder Ballery
 * Clears test data while preserving essential data
 */

const mongoose = require('mongoose');
const Review = require('../models/Review');
const Booking = require('../models/Booking');
const Inquiry = require('../models/Inquiry');
const Service = require('../models/Service');
const Portfolio = require('../models/Portfolio');
const User = require('../models/User');
require('dotenv').config();

async function resetDatabase() {
    try {
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/builder_ballery');
        console.log('✅ Connected to MongoDB');

        console.log('\n🧹 Starting database cleanup...');

        // Get counts before cleanup
        const beforeCounts = {
            reviews: await Review.countDocuments(),
            bookings: await Booking.countDocuments(),
            inquiries: await Inquiry.countDocuments(),
            services: await Service.countDocuments(),
            portfolio: await Portfolio.countDocuments(),
            users: await User.countDocuments()
        };

        console.log('\n📊 Before cleanup:');
        Object.entries(beforeCounts).forEach(([collection, count]) => {
            console.log(`   ${collection}: ${count} documents`);
        });

        // Clear test data collections
        console.log('\n🗑️  Clearing test data...');
        
        // Clear all reviews
        const deletedReviews = await Review.deleteMany({});
        console.log(`   ✅ Deleted ${deletedReviews.deletedCount} reviews`);

        // Clear all bookings
        const deletedBookings = await Booking.deleteMany({});
        console.log(`   ✅ Deleted ${deletedBookings.deletedCount} bookings`);

        // Clear all inquiries
        const deletedInquiries = await Inquiry.deleteMany({});
        console.log(`   ✅ Deleted ${deletedInquiries.deletedCount} inquiries`);

        // Keep services, portfolio, and users as they are essential data
        console.log('\n💾 Preserving essential data:');
        console.log(`   ✅ Services: ${beforeCounts.services} documents preserved`);
        console.log(`   ✅ Portfolio: ${beforeCounts.portfolio} documents preserved`);
        console.log(`   ✅ Users: ${beforeCounts.users} documents preserved`);

        // Get counts after cleanup
        const afterCounts = {
            reviews: await Review.countDocuments(),
            bookings: await Booking.countDocuments(),
            inquiries: await Inquiry.countDocuments(),
            services: await Service.countDocuments(),
            portfolio: await Portfolio.countDocuments(),
            users: await User.countDocuments()
        };

        console.log('\n📊 After cleanup:');
        Object.entries(afterCounts).forEach(([collection, count]) => {
            console.log(`   ${collection}: ${count} documents`);
        });

        // Verify cleanup
        console.log('\n🔍 Verification:');
        if (afterCounts.reviews === 0 && afterCounts.bookings === 0 && afterCounts.inquiries === 0) {
            console.log('   ✅ All test data successfully cleared');
        } else {
            console.log('   ⚠️  Some data may not have been cleared properly');
        }

        if (afterCounts.services > 0 && afterCounts.users > 0) {
            console.log('   ✅ Essential data preserved');
        } else {
            console.log('   ⚠️  Essential data may have been affected');
        }

        console.log('\n🎉 Database reset completed successfully!');
        console.log('\n📝 Next steps:');
        console.log('   1. Restart the Node.js backend server');
        console.log('   2. Test frontend form submissions');
        console.log('   3. Verify admin panel functionality');
        console.log('   4. Check analytics dashboard updates');

    } catch (error) {
        console.error('❌ Error resetting database:', error);
        throw error;
    } finally {
        await mongoose.disconnect();
        console.log('\n🔌 Disconnected from MongoDB');
    }
}

// Additional utility functions
async function getCollectionStats() {
    try {
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/builder_ballery');
        
        const stats = {
            reviews: await Review.countDocuments(),
            bookings: await Booking.countDocuments(),
            inquiries: await Inquiry.countDocuments(),
            services: await Service.countDocuments(),
            portfolio: await Portfolio.countDocuments(),
            users: await User.countDocuments()
        };

        console.log('\n📊 Current Database Statistics:');
        Object.entries(stats).forEach(([collection, count]) => {
            console.log(`   ${collection}: ${count} documents`);
        });

        return stats;
    } catch (error) {
        console.error('❌ Error getting collection stats:', error);
        throw error;
    } finally {
        await mongoose.disconnect();
    }
}

// Run the script
if (require.main === module) {
    const command = process.argv[2];
    
    if (command === 'stats') {
        getCollectionStats();
    } else {
        resetDatabase();
    }
}

module.exports = { resetDatabase, getCollectionStats };
