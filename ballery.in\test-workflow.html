<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Builder Ballery - Workflow Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        label {
            font-weight: bold;
            color: #333;
        }
        input, textarea {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            padding: 12px 24px;
            background-color: #2c5aa0;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
        }
        button:hover {
            background-color: #1e3f73;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-results {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .link-button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
        }
        .link-button:hover {
            background-color: #1e7e34;
        }
    </style>
</head>
<body>
    <h1>🧪 Builder Ballery Workflow Test</h1>
    
    <div class="test-section">
        <h2>📋 System Status Check</h2>
        <div id="system-status">
            <p>Checking system status...</p>
        </div>
        <button onclick="checkSystemStatus()">🔄 Check Status</button>
    </div>

    <div class="test-section">
        <h2>📝 Test Form Submission</h2>
        <p>Use this form to test the complete workflow:</p>
        
        <form id="test-form" class="test-form">
            <div class="form-group">
                <label for="test-name">Your Name *</label>
                <input type="text" id="test-name" name="name" required value="Test User">
            </div>
            
            <div class="form-group">
                <label for="test-phone">Phone Number *</label>
                <input type="tel" id="test-phone" name="phone" required value="+919876543000">
            </div>
            
            <div class="form-group">
                <label for="test-message">Message</label>
                <textarea id="test-message" name="message" rows="3">Testing the Builder Ballery workflow from the test page.</textarea>
            </div>
            
            <button type="submit">🚀 Submit Test Inquiry</button>
        </form>
        
        <div id="form-status"></div>
    </div>

    <div class="test-section">
        <h2>📊 Check Results</h2>
        <p>After submitting the form, check these locations:</p>
        
        <div>
            <a href="http://localhost:8080/admin-live.html" target="_blank" class="link-button">
                🔧 Open Admin Panel
            </a>
            <a href="http://localhost:8080" target="_blank" class="link-button">
                🏠 Open Main Website
            </a>
        </div>
        
        <div class="test-results">
            <h3>📈 Latest Inquiries</h3>
            <div id="inquiries-list">
                <p>Click "Load Inquiries" to see current data...</p>
            </div>
            <button onclick="loadInquiries()">📋 Load Inquiries</button>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3000/api';

        // Check system status
        async function checkSystemStatus() {
            const statusDiv = document.getElementById('system-status');
            statusDiv.innerHTML = '<p>🔄 Checking...</p>';

            try {
                // Check backend health
                const healthResponse = await fetch(`${API_BASE_URL}/health`);
                const healthData = await healthResponse.json();

                // Check inquiries endpoint
                const inquiriesResponse = await fetch(`${API_BASE_URL}/inquiries`, {
                    headers: {
                        'Authorization': 'Bearer demo-token'
                    }
                });
                const inquiriesData = await inquiriesResponse.json();

                statusDiv.innerHTML = `
                    <div class="success">
                        ✅ Backend API: ${healthData.status} (Uptime: ${Math.round(healthData.uptime)}s)<br>
                        ✅ Database: Connected<br>
                        ✅ Inquiries: ${inquiriesData.data?.inquiries?.length || 0} total<br>
                        ✅ CORS: Working
                    </div>
                `;
            } catch (error) {
                statusDiv.innerHTML = `
                    <div class="error">
                        ❌ System Error: ${error.message}<br>
                        Make sure both servers are running:<br>
                        - Frontend: http://localhost:8080<br>
                        - Backend: http://localhost:3000
                    </div>
                `;
            }
        }

        // Handle form submission
        document.getElementById('test-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const statusDiv = document.getElementById('form-status');
            const submitBtn = this.querySelector('button[type="submit"]');
            
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.textContent = '⏳ Submitting...';
            statusDiv.innerHTML = '<div class="info">📤 Submitting your test inquiry...</div>';

            try {
                const formData = new FormData(this);
                const data = Object.fromEntries(formData.entries());

                const response = await fetch(`${API_BASE_URL}/inquiries/callback`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                
                statusDiv.innerHTML = `
                    <div class="success">
                        ✅ Success! Inquiry submitted successfully.<br>
                        📋 Inquiry ID: ${result.data.inquiryId}<br>
                        👤 Name: ${result.data.name}<br>
                        📞 Phone: ${result.data.phone}<br>
                        <br>
                        <strong>Next Steps:</strong><br>
                        1. Open the Admin Panel (link above)<br>
                        2. Click "💬 Inquiries" in the sidebar<br>
                        3. Look for your inquiry in the table
                    </div>
                `;

                // Auto-load inquiries to show the result
                setTimeout(loadInquiries, 1000);

            } catch (error) {
                statusDiv.innerHTML = `
                    <div class="error">
                        ❌ Submission failed: ${error.message}<br>
                        Please check that the backend server is running on port 3000.
                    </div>
                `;
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '🚀 Submit Test Inquiry';
            }
        });

        // Load and display inquiries
        async function loadInquiries() {
            const inquiriesDiv = document.getElementById('inquiries-list');
            inquiriesDiv.innerHTML = '<p>🔄 Loading inquiries...</p>';

            try {
                const response = await fetch(`${API_BASE_URL}/inquiries`, {
                    headers: {
                        'Authorization': 'Bearer demo-token'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                const inquiries = data.data?.inquiries || [];

                if (inquiries.length === 0) {
                    inquiriesDiv.innerHTML = '<p>📭 No inquiries found. Submit the test form above!</p>';
                    return;
                }

                const inquiriesHtml = inquiries.slice(0, 5).map(inquiry => `
                    <div style="border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 4px;">
                        <strong>${inquiry.name}</strong> - ${inquiry.subject}<br>
                        <small>📞 ${inquiry.phone || 'No phone'} | 📧 ${inquiry.email || 'No email'}</small><br>
                        <small>🕒 ${new Date(inquiry.createdAt).toLocaleString()}</small><br>
                        <small>📍 Source: ${inquiry.source} | Status: ${inquiry.status}</small>
                    </div>
                `).join('');

                inquiriesDiv.innerHTML = `
                    <p><strong>📊 Total: ${inquiries.length} inquiries</strong></p>
                    ${inquiriesHtml}
                    ${inquiries.length > 5 ? '<p><em>Showing latest 5 inquiries...</em></p>' : ''}
                `;

            } catch (error) {
                inquiriesDiv.innerHTML = `
                    <div class="error">
                        ❌ Failed to load inquiries: ${error.message}
                    </div>
                `;
            }
        }

        // Auto-check status on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkSystemStatus();
        });
    </script>
</body>
</html>
