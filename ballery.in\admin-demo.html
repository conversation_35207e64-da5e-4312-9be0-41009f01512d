<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Builder Ballery - Admin Dashboard</title>
    <link rel="stylesheet" href="assets/admin-dashboard.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f1f1f1;
            color: #333;
        }
        
        .admin-header {
            background: #2c5aa0;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .admin-logo {
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .admin-user {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .admin-sidebar {
            position: fixed;
            left: 0;
            top: 70px;
            width: 250px;
            height: calc(100vh - 70px);
            background: white;
            box-shadow: 2px 0 4px rgba(0,0,0,0.1);
            overflow-y: auto;
        }
        
        .admin-nav {
            list-style: none;
            padding: 20px 0;
        }
        
        .admin-nav li {
            margin: 0;
        }
        
        .admin-nav a {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 20px;
            color: #333;
            text-decoration: none;
            transition: background-color 0.2s ease;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background: #f8f9fa;
            color: #2c5aa0;
        }
        
        .admin-content {
            margin-left: 250px;
            padding: 20px;
            min-height: calc(100vh - 70px);
        }
        
        .page-header {
            background: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .page-header h1 {
            color: #2c5aa0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 15px;
            transition: transform 0.2s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
        }
        
        .stat-icon {
            font-size: 2.5rem;
            opacity: 0.8;
        }
        
        .stat-content h3 {
            margin: 0;
            font-size: 2rem;
            font-weight: bold;
            color: #2c5aa0;
        }
        
        .stat-content p {
            margin: 5px 0;
            font-weight: 600;
            color: #333;
        }
        
        .recent-activity {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .activity-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .item-icon {
            font-size: 1.5rem;
        }
        
        .item-content h4 {
            margin: 0 0 5px 0;
            font-size: 14px;
            font-weight: 600;
        }
        
        .item-content p {
            margin: 0 0 5px 0;
            font-size: 13px;
            color: #666;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-pending { background: #fff3cd; color: #856404; }
        .status-confirmed { background: #d4edda; color: #155724; }
        .status-completed { background: #cce5ff; color: #004085; }
        
        @media (max-width: 768px) {
            .admin-sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .admin-content {
                margin-left: 0;
            }
            
            .recent-activity {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Admin Header -->
    <header class="admin-header">
        <div class="admin-logo">
            🏗️ BUILDER BALLERY - Admin Panel
        </div>
        <div class="admin-user">
            <span>Welcome, Admin</span>
            <span>👤</span>
        </div>
    </header>

    <!-- Admin Sidebar -->
    <nav class="admin-sidebar">
        <ul class="admin-nav">
            <li><a href="#dashboard" class="nav-link active">📊 Dashboard</a></li>
            <li><a href="#bookings" class="nav-link">📅 Bookings</a></li>
            <li><a href="#inquiries" class="nav-link">💬 Inquiries</a></li>
            <li><a href="#services" class="nav-link">🛠️ Services</a></li>
            <li><a href="#portfolio" class="nav-link">🏗️ Portfolio</a></li>
            <li><a href="#customers" class="nav-link">👥 Customers</a></li>
            <li><a href="#reports" class="nav-link">📈 Reports</a></li>
            <li><a href="#settings" class="nav-link">⚙️ Settings</a></li>
        </ul>
    </nav>

    <!-- Main Content -->
    <main class="admin-content">
        <!-- Dashboard Page -->
        <div id="dashboard-page" class="admin-page">
            <div class="page-header">
                <h1>📊 Dashboard Overview</h1>
                <p>Welcome to your Builder Ballery admin dashboard</p>
            </div>

            <!-- Statistics Grid -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">📅</div>
                    <div class="stat-content">
                        <h3>47</h3>
                        <p>Total Bookings</p>
                        <small>+12 this month</small>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">💰</div>
                    <div class="stat-content">
                        <h3>₹2,45,000</h3>
                        <p>Revenue</p>
                        <small>+18% from last month</small>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">💬</div>
                    <div class="stat-content">
                        <h3>23</h3>
                        <p>New Inquiries</p>
                        <small>5 unread</small>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">⭐</div>
                    <div class="stat-content">
                        <h3>4.8</h3>
                        <p>Avg Rating</p>
                        <small>Based on 34 reviews</small>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="recent-activity">
                <div class="activity-section">
                    <h2>Recent Bookings</h2>
                    
                    <div class="activity-item">
                        <div class="item-icon">🏠</div>
                        <div class="item-content">
                            <h4>Site Visit - Residential</h4>
                            <p>Priya Sharma - Bangalore</p>
                            <small>2 hours ago</small>
                        </div>
                        <span class="status-badge status-pending">Pending</span>
                    </div>
                    
                    <div class="activity-item">
                        <div class="item-icon">💻</div>
                        <div class="item-content">
                            <h4>Virtual Consultation</h4>
                            <p>Rajesh Kumar - Pune</p>
                            <small>5 hours ago</small>
                        </div>
                        <span class="status-badge status-confirmed">Confirmed</span>
                    </div>
                    
                    <div class="activity-item">
                        <div class="item-icon">🏗️</div>
                        <div class="item-content">
                            <h4>Quality Inspection</h4>
                            <p>Anita Desai - Chennai</p>
                            <small>1 day ago</small>
                        </div>
                        <span class="status-badge status-completed">Completed</span>
                    </div>
                </div>
                
                <div class="activity-section">
                    <h2>Recent Inquiries</h2>
                    
                    <div class="activity-item">
                        <div class="item-icon">❓</div>
                        <div class="item-content">
                            <h4>Foundation Inspection Query</h4>
                            <p>Need help with foundation quality check</p>
                            <small>1 hour ago</small>
                        </div>
                        <span class="status-badge status-pending">New</span>
                    </div>
                    
                    <div class="activity-item">
                        <div class="item-icon">📋</div>
                        <div class="item-content">
                            <h4>Plan Review Request</h4>
                            <p>Architectural drawings review needed</p>
                            <small>3 hours ago</small>
                        </div>
                        <span class="status-badge status-confirmed">Responded</span>
                    </div>
                    
                    <div class="activity-item">
                        <div class="item-icon">💡</div>
                        <div class="item-content">
                            <h4>Cost Estimation Help</h4>
                            <p>Budget planning for 2BHK construction</p>
                            <small>6 hours ago</small>
                        </div>
                        <span class="status-badge status-completed">Resolved</span>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Simple navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Remove active class from all links
                document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                
                // Add active class to clicked link
                this.classList.add('active');
                
                // Show corresponding page (for demo, just update header)
                const page = this.textContent.trim();
                document.querySelector('.page-header h1').textContent = page;
                document.querySelector('.page-header p').textContent = `Manage your ${page.toLowerCase()}`;
            });
        });
    </script>
</body>
</html>
