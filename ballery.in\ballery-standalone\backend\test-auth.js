/**
 * Test auth middleware import
 */

const express = require('express');
require('dotenv').config();

console.log('Testing auth middleware import...');

try {
    const authMiddleware = require('./middleware/auth');
    console.log('✅ Auth middleware imported successfully');
    console.log('Available exports:', Object.keys(authMiddleware));
    
    const { auth } = authMiddleware;
    console.log('✅ Auth function extracted:', typeof auth);
    
    if (typeof auth === 'function') {
        console.log('✅ Auth is a function - middleware should work');
    } else {
        console.log('❌ Auth is not a function:', auth);
    }
    
} catch (error) {
    console.error('❌ Error importing auth middleware:', error);
}

console.log('\nTesting User model import...');

try {
    const User = require('./models/User');
    console.log('✅ User model imported successfully');
    console.log('User model type:', typeof User);
    
} catch (error) {
    console.error('❌ Error importing User model:', error);
}

console.log('\nTest complete.');
