/**
 * BUILDER BALLERY - Database Seeder
 * Populate database with comprehensive test data
 */

const mongoose = require('mongoose');
const User = require('../models/User');
const Inquiry = require('../models/Inquiry');
const Booking = require('../models/Booking');
const Service = require('../models/Service');
require('dotenv').config();

// Sample data
const sampleUsers = [
    {
        username: 'admin',
        email: '<EMAIL>',
        password: 'admin123',
        role: 'admin',
        permissions: [
            'bookings.view', 'bookings.create', 'bookings.edit', 'bookings.delete',
            'inquiries.view', 'inquiries.respond', 'inquiries.delete',
            'services.view', 'services.edit',
            'portfolio.view', 'portfolio.edit',
            'users.view', 'users.edit',
            'settings.view', 'settings.edit',
            'analytics.view', 'export.data'
        ],
        profile: {
            firstName: 'Admin',
            lastName: 'User',
            phone: '+91 98765 43210'
        }
    }
];

const sampleServices = [
    {
        serviceName: 'Virtual Construction Consultation',
        serviceSlug: 'virtual-construction-consultation',
        serviceType: 'virtual',
        price: 2500,
        description: 'Get professional construction consultation from the comfort of your home. Our experts will review your plans, discuss challenges, and provide actionable recommendations.',
        shortDescription: 'Expert construction advice via video call with detailed project analysis',
        estimatedDuration: {
            value: 60,
            unit: 'minutes'
        },
        displayOrder: 1,
        isActive: true,
        category: 'consultation'
    },
    {
        serviceName: 'Site Visit & Inspection',
        serviceSlug: 'site-visit-inspection',
        serviceType: 'site-visit',
        price: 5000,
        description: 'Comprehensive on-site inspection covering foundation, structure, electrical, plumbing, and finishing work with detailed report.',
        shortDescription: 'On-site inspection and quality assessment by certified engineers',
        estimatedDuration: {
            value: 2,
            unit: 'hours'
        },
        displayOrder: 2,
        isActive: true,
        category: 'inspection'
    },
    {
        serviceName: 'Plan Review & Approval',
        serviceSlug: 'plan-review-approval',
        serviceType: 'virtual',
        price: 3500,
        description: 'Professional review of your architectural plans with suggestions for optimization, compliance check, and approval guidance.',
        shortDescription: 'Architectural plan review and approval guidance',
        estimatedDuration: {
            value: 90,
            unit: 'minutes'
        },
        displayOrder: 3,
        isActive: true,
        category: 'review'
    },
    {
        serviceName: 'Quality Audit',
        serviceSlug: 'quality-audit',
        serviceType: 'site-visit',
        price: 7500,
        description: 'Detailed quality audit covering all aspects of construction with photographic documentation and improvement recommendations.',
        shortDescription: 'Comprehensive quality audit of ongoing construction',
        estimatedDuration: {
            value: 3,
            unit: 'hours'
        },
        displayOrder: 4,
        isActive: true,
        category: 'inspection',
        isPopular: true
    },
    {
        serviceName: 'Material Advisory',
        serviceSlug: 'material-advisory',
        serviceType: 'virtual',
        price: 2000,
        description: 'Professional advice on material selection, quality standards, cost optimization, and vendor recommendations.',
        shortDescription: 'Expert guidance on material selection and cost optimization',
        estimatedDuration: {
            value: 45,
            unit: 'minutes'
        },
        displayOrder: 5,
        isActive: true,
        category: 'advisory'
    }
];

const sampleInquiries = [
    {
        name: 'Priya Sharma',
        email: '<EMAIL>',
        phone: '+91 98765 43211',
        subject: 'Foundation Quality Check',
        message: 'I need help with foundation quality inspection for my 2BHK construction project in Bangalore. The foundation work is completed and I want to ensure it meets quality standards.',
        inquiryType: 'support',
        status: 'unread',
        source: 'website_contact'
    },
    {
        name: 'Rajesh Kumar',
        email: '<EMAIL>',
        phone: '+91 98765 43212',
        subject: 'Virtual Consultation for New Project',
        message: 'Planning to start construction of a 3BHK house in Pune. Need expert guidance on plan approval and material selection.',
        inquiryType: 'general',
        status: 'read',
        source: 'website_contact'
    },
    {
        name: 'Anita Desai',
        email: '<EMAIL>',
        phone: '+91 98765 43213',
        subject: 'Quality Inspection Required',
        message: 'My construction is 70% complete. Need comprehensive quality inspection to identify any issues before final finishing work.',
        inquiryType: 'booking',
        status: 'responded',
        adminResponse: 'Thank you for your inquiry. We have scheduled a quality inspection for your project. Our engineer will visit your site within 2 days.',
        source: 'website_contact'
    },
    {
        name: 'Vikram Singh',
        email: '<EMAIL>',
        phone: '+91 98765 43214',
        subject: 'Cost Estimation Help',
        message: 'Need help with cost estimation for 1500 sq ft independent house construction in Chennai. Please provide detailed breakdown.',
        inquiryType: 'pricing',
        status: 'resolved',
        adminResponse: 'We have prepared a detailed cost estimation for your project. Please find the breakdown attached. Total estimated cost is ₹18-22 lakhs.',
        source: 'website_callback'
    },
    {
        name: 'Meera Patel',
        email: '<EMAIL>',
        phone: '+91 98765 43215',
        subject: 'Electrical Work Inspection',
        message: 'Electrical work is completed in my apartment renovation. Need expert inspection to ensure safety and compliance.',
        inquiryType: 'support',
        status: 'unread',
        source: 'website_contact'
    },
    {
        name: 'Arjun Reddy',
        email: '<EMAIL>',
        phone: '+91 98765 43216',
        subject: 'Plan Approval Guidance',
        message: 'Having issues with plan approval from local authorities. Need expert guidance on compliance requirements.',
        inquiryType: 'general',
        status: 'read',
        source: 'website_contact'
    },
    {
        name: 'Kavya Nair',
        email: '<EMAIL>',
        phone: '+91 98765 43217',
        subject: 'Material Quality Concerns',
        message: 'Concerned about the quality of materials being used by my contractor. Need expert opinion on cement and steel quality.',
        inquiryType: 'support',
        status: 'responded',
        adminResponse: 'We understand your concern about material quality. Our expert will visit your site to inspect the materials and provide a detailed quality report.',
        source: 'website_contact'
    }
];

const sampleBookings = [
    {
        clientName: 'Priya Sharma',
        clientEmail: '<EMAIL>',
        clientPhone: '+91 98765 43211',
        serviceType: 'site-visit',
        services: ['Site Visit & Inspection'],
        preferredDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
        preferredTime: 'morning',
        projectDescription: 'Foundation quality inspection for 2BHK construction',
        siteLocation: 'Whitefield, Bangalore, Karnataka',
        status: 'confirmed',
        totalAmount: 5000,
        projectType: 'residential',
        projectStage: 'construction'
    },
    {
        clientName: 'Rajesh Kumar',
        clientEmail: '<EMAIL>',
        clientPhone: '+91 98765 43212',
        serviceType: 'virtual',
        services: ['Virtual Construction Consultation'],
        preferredDate: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000), // 1 day from now
        preferredTime: 'afternoon',
        projectDescription: 'Virtual consultation for 3BHK house construction planning',
        siteLocation: 'Pune, Maharashtra',
        status: 'confirmed',
        totalAmount: 2500,
        projectType: 'residential',
        projectStage: 'planning'
    },
    {
        clientName: 'Anita Desai',
        clientEmail: '<EMAIL>',
        clientPhone: '+91 98765 43213',
        serviceType: 'site-visit',
        services: ['Quality Audit'],
        preferredDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
        preferredTime: 'morning',
        projectDescription: 'Comprehensive quality inspection for 70% completed construction',
        siteLocation: 'Anna Nagar, Chennai, Tamil Nadu',
        status: 'confirmed',
        totalAmount: 7500,
        projectType: 'residential',
        projectStage: 'finishing'
    },
    {
        clientName: 'Vikram Singh',
        clientEmail: '<EMAIL>',
        clientPhone: '+91 98765 43214',
        serviceType: 'virtual',
        services: ['Material Advisory'],
        preferredDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
        preferredTime: 'flexible',
        projectDescription: 'Cost estimation and material advisory for independent house',
        siteLocation: 'Chennai, Tamil Nadu',
        status: 'pending',
        totalAmount: 2000,
        projectType: 'residential',
        projectStage: 'planning'
    }
];

async function seedDatabase() {
    try {
        console.log('🌱 Starting database seeding...');

        // Connect to MongoDB
        await mongoose.connect(process.env.MONGODB_URI, {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });
        console.log('✅ Connected to MongoDB');

        // Clear existing data
        console.log('🧹 Clearing existing data...');
        await Promise.all([
            User.deleteMany({}),
            Inquiry.deleteMany({}),
            Booking.deleteMany({}),
            Service.deleteMany({})
        ]);

        // Seed Users
        console.log('👤 Seeding users...');
        const users = await User.create(sampleUsers);
        console.log(`✅ Created ${users.length} users`);

        // Seed Services
        console.log('🛠️ Seeding services...');
        const services = await Service.create(sampleServices);
        console.log(`✅ Created ${services.length} services`);

        // Seed Inquiries
        console.log('💬 Seeding inquiries...');
        const inquiries = await Inquiry.create(sampleInquiries);
        console.log(`✅ Created ${inquiries.length} inquiries`);

        // Seed Bookings
        console.log('📅 Seeding bookings...');
        const bookings = await Booking.create(sampleBookings);
        console.log(`✅ Created ${bookings.length} bookings`);

        console.log('🎉 Database seeding completed successfully!');
        console.log('\n📊 Summary:');
        console.log(`👤 Users: ${users.length}`);
        console.log(`🛠️ Services: ${services.length}`);
        console.log(`💬 Inquiries: ${inquiries.length}`);
        console.log(`📅 Bookings: ${bookings.length}`);

        console.log('\n🔑 Admin Login Credentials:');
        console.log('Username: admin');
        console.log('Password: admin123');
        console.log('Email: <EMAIL>');

        process.exit(0);

    } catch (error) {
        console.error('❌ Database seeding failed:', error);
        process.exit(1);
    }
}

// Run seeder if called directly
if (require.main === module) {
    seedDatabase();
}

module.exports = { seedDatabase };
