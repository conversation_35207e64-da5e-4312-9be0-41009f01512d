/**
 * Simple server to test auth routes
 */

const express = require('express');
const mongoose = require('mongoose');
require('dotenv').config();

const app = express();
const PORT = 3001;

// Basic middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Test route
app.get('/', (req, res) => {
    res.json({ message: 'Simple server running', status: 'OK' });
});

// Import and use simple auth routes
try {
    const simpleAuthRoutes = require('./simple-auth');
    app.use('/api/auth', simpleAuthRoutes);
    console.log('✅ Simple auth routes loaded successfully');
} catch (error) {
    console.error('❌ Error loading simple auth routes:', error);
}

// Start server
const server = app.listen(PORT, () => {
    console.log(`🚀 Simple server running on port ${PORT}`);
    console.log(`🔗 Test URL: http://localhost:${PORT}`);
    console.log(`🔗 Auth test: http://localhost:${PORT}/api/auth/test`);
});

server.on('error', (error) => {
    if (error.code === 'EADDRINUSE') {
        console.error(`❌ Port ${PORT} is already in use`);
        process.exit(1);
    } else {
        console.error('❌ Server error:', error);
    }
});

module.exports = app;
