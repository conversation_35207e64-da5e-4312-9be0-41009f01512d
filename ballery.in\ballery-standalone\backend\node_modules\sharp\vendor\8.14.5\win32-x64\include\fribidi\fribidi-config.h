/* fribidi-config.h file generated by <PERSON><PERSON> */
/* Not copyrighted, in public domain. */
#ifndef FRIBIDI_CONFIG_H
#define FRIBIDI_CONFIG_H

#define FRIBIDI "fribidi"
#define FRIBIDI_NAME "GNU FriBidi"
#define FRIBIDI_BUGREPORT "https://github.com/fribidi/fribidi/issues/new"

#define FRIBIDI_VERSION "1.0.13"
#define FRIBIDI_MAJOR_VERSION 1
#define FRIBIDI_MINOR_VERSION 0
#define FRIBIDI_MICRO_VERSION 13
#define FRIBIDI_INTERFACE_VERSION 4
#define FRIBIDI_INTERFACE_VERSION_STRING "4"

/* The size of a `int', as computed by sizeof. */
#define FRIBIDI_SIZEOF_INT 4

/* Define if fribidi was built with MSVC */
#undef FRIBIDI_BUILT_WITH_MSVC

#endif /* FRIBIDI_CONFIG_H */
