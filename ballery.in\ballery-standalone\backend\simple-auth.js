/**
 * Simple auth routes test
 */

const express = require('express');
const { body, validationResult } = require('express-validator');
const { auth } = require('./middleware/auth');

const router = express.Router();

/**
 * @route   POST /api/auth/login
 * @desc    Admin login (simplified)
 * @access  Public
 */
router.post('/login', [
    body('identifier').notEmpty().withMessage('Username or email is required'),
    body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters long')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        // Simplified response for testing
        res.json({
            success: true,
            message: 'Login endpoint working',
            data: { test: true }
        });

    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

/**
 * @route   POST /api/auth/logout
 * @desc    Admin logout (simplified)
 * @access  Private
 */
router.post('/logout', auth, async (req, res) => {
    try {
        res.json({
            success: true,
            message: 'Logout endpoint working'
        });

    } catch (error) {
        console.error('Logout error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

/**
 * @route   GET /api/auth/test
 * @desc    Test endpoint
 * @access  Public
 */
router.get('/test', (req, res) => {
    res.json({
        success: true,
        message: 'Auth routes are working!',
        timestamp: new Date().toISOString()
    });
});

module.exports = router;
