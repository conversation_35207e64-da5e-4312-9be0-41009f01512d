/**
 * BUILDER BALLERY - Email Service
 * Handle email sending and templates
 */

const nodemailer = require('nodemailer');
const fs = require('fs').promises;
const path = require('path');

class EmailService {
    constructor() {
        this.transporter = null;
        this.initializeTransporter();
    }

    async initializeTransporter() {
        try {
            // Create transporter based on environment
            if (process.env.NODE_ENV === 'production') {
                // Production SMTP configuration
                this.transporter = nodemailer.createTransport({
                    host: process.env.SMTP_HOST,
                    port: process.env.SMTP_PORT,
                    secure: process.env.SMTP_SECURE === 'true',
                    auth: {
                        user: process.env.SMTP_USER,
                        pass: process.env.SMTP_PASS
                    }
                });
            } else {
                // Development - use Ethereal Email for testing
                const testAccount = await nodemailer.createTestAccount();
                
                this.transporter = nodemailer.createTransport({
                    host: 'smtp.ethereal.email',
                    port: 587,
                    secure: false,
                    auth: {
                        user: testAccount.user,
                        pass: testAccount.pass
                    }
                });
                
                console.log('📧 Email Service initialized with test account');
                console.log('📧 Test emails will be available at: https://ethereal.email');
            }

            // Verify connection
            await this.transporter.verify();
            console.log('✅ Email service ready');
            
        } catch (error) {
            console.error('❌ Email service initialization failed:', error);
            this.transporter = null;
        }
    }

    async sendEmail(options) {
        if (!this.transporter) {
            throw new Error('Email service not initialized');
        }

        try {
            const mailOptions = {
                from: `${process.env.FROM_NAME || 'BUILDER BALLERY'} <${process.env.FROM_EMAIL || '<EMAIL>'}>`,
                to: options.to,
                subject: options.subject,
                html: options.html,
                text: options.text
            };

            const info = await this.transporter.sendMail(mailOptions);
            
            // Log preview URL for development
            if (process.env.NODE_ENV !== 'production') {
                console.log('📧 Email sent:', nodemailer.getTestMessageUrl(info));
            }

            return {
                success: true,
                messageId: info.messageId,
                previewUrl: process.env.NODE_ENV !== 'production' ? nodemailer.getTestMessageUrl(info) : null
            };
            
        } catch (error) {
            console.error('Email sending failed:', error);
            throw error;
        }
    }

    // Email Templates
    generateInquiryResponseTemplate(inquiry, response) {
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Response from Builder Ballery</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: #2c5aa0; color: white; padding: 20px; text-align: center; }
                    .content { padding: 30px 20px; background: #f9f9f9; }
                    .response-box { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2c5aa0; }
                    .footer { background: #333; color: white; padding: 20px; text-align: center; font-size: 14px; }
                    .btn { display: inline-block; background: #2c5aa0; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; margin: 10px 0; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🏗️ BUILDER BALLERY</h1>
                        <p>Your Construction Expert</p>
                    </div>
                    
                    <div class="content">
                        <h2>Hello ${inquiry.name},</h2>
                        
                        <p>Thank you for contacting Builder Ballery. We have reviewed your inquiry and here's our response:</p>
                        
                        <div class="response-box">
                            <h3>Your Inquiry:</h3>
                            <p><strong>Subject:</strong> ${inquiry.subject}</p>
                            <p><strong>Message:</strong> ${inquiry.message}</p>
                            
                            <h3>Our Response:</h3>
                            <p>${response.replace(/\n/g, '<br>')}</p>
                        </div>
                        
                        <p>If you have any follow-up questions or need further assistance, please don't hesitate to contact us.</p>
                        
                        <a href="tel:+919876543210" class="btn">📞 Call Us Now</a>
                        <a href="https://wa.me/919876543210" class="btn">💬 WhatsApp</a>
                    </div>
                    
                    <div class="footer">
                        <p><strong>BUILDER BALLERY</strong></p>
                        <p>📧 <EMAIL> | 📞 +91 98765 43210</p>
                        <p>🌐 www.builderballery.com</p>
                        <p>Your trusted construction consultation partner</p>
                    </div>
                </div>
            </body>
            </html>
        `;
    }

    generateWelcomeTemplate(inquiry) {
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Thank you for contacting Builder Ballery</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: #2c5aa0; color: white; padding: 20px; text-align: center; }
                    .content { padding: 30px 20px; background: #f9f9f9; }
                    .info-box { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
                    .footer { background: #333; color: white; padding: 20px; text-align: center; font-size: 14px; }
                    .btn { display: inline-block; background: #2c5aa0; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; margin: 10px 0; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🏗️ BUILDER BALLERY</h1>
                        <p>Thank You for Contacting Us!</p>
                    </div>
                    
                    <div class="content">
                        <h2>Hello ${inquiry.name},</h2>
                        
                        <p>We have received your inquiry and our expert team is reviewing it. Here are the details we received:</p>
                        
                        <div class="info-box">
                            <p><strong>Inquiry ID:</strong> ${inquiry.inquiryId}</p>
                            <p><strong>Subject:</strong> ${inquiry.subject}</p>
                            <p><strong>Submitted:</strong> ${new Date(inquiry.submittedAt).toLocaleString('en-IN')}</p>
                        </div>
                        
                        <p><strong>What happens next?</strong></p>
                        <ul>
                            <li>Our expert will review your inquiry within 2 hours</li>
                            <li>You'll receive a detailed response via email</li>
                            <li>For urgent matters, call us directly</li>
                        </ul>
                        
                        <p>Need immediate assistance? Contact us directly:</p>
                        
                        <a href="tel:+919876543210" class="btn">📞 Call Now</a>
                        <a href="https://wa.me/919876543210" class="btn">💬 WhatsApp</a>
                    </div>
                    
                    <div class="footer">
                        <p><strong>BUILDER BALLERY</strong></p>
                        <p>📧 <EMAIL> | 📞 +91 98765 43210</p>
                        <p>🌐 www.builderballery.com</p>
                        <p>Your trusted construction consultation partner</p>
                    </div>
                </div>
            </body>
            </html>
        `;
    }

    // Service methods
    async sendInquiryResponse(inquiry, response) {
        const html = this.generateInquiryResponseTemplate(inquiry, response);
        
        return await this.sendEmail({
            to: inquiry.email,
            subject: `Re: ${inquiry.subject} - Response from Builder Ballery`,
            html: html,
            text: `Hello ${inquiry.name},\n\nThank you for contacting Builder Ballery. Here's our response to your inquiry:\n\n${response}\n\nBest regards,\nBuilder Ballery Team`
        });
    }

    async sendWelcomeEmail(inquiry) {
        const html = this.generateWelcomeTemplate(inquiry);
        
        return await this.sendEmail({
            to: inquiry.email,
            subject: `Thank you for contacting Builder Ballery - ${inquiry.inquiryId}`,
            html: html,
            text: `Hello ${inquiry.name},\n\nThank you for contacting Builder Ballery. We have received your inquiry (ID: ${inquiry.inquiryId}) and will respond within 2 hours during business hours.\n\nBest regards,\nBuilder Ballery Team`
        });
    }

    async sendBulkEmail(recipients, subject, content) {
        const results = [];
        
        for (const recipient of recipients) {
            try {
                const result = await this.sendEmail({
                    to: recipient.email,
                    subject: subject,
                    html: content,
                    text: content.replace(/<[^>]*>/g, '') // Strip HTML for text version
                });
                
                results.push({
                    email: recipient.email,
                    success: true,
                    messageId: result.messageId
                });
                
            } catch (error) {
                results.push({
                    email: recipient.email,
                    success: false,
                    error: error.message
                });
            }
        }
        
        return results;
    }
}

// Create singleton instance
const emailService = new EmailService();

module.exports = emailService;
