/**
 * BUILDER BALLERY - Authentication Middleware
 * JWT token verification and user authentication
 */

const jwt = require('jsonwebtoken');
const User = require('../models/User');

/**
 * Authentication middleware
 * Verifies JWT token and attaches user to request
 */
const auth = async (req, res, next) => {
    try {
        // Get token from header
        const authHeader = req.header('Authorization');
        
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({
                success: false,
                message: 'Access denied. No token provided.'
            });
        }

        // Extract token
        const token = authHeader.substring(7); // Remove 'Bearer ' prefix

        // Demo mode - allow demo-token for testing
        if (process.env.NODE_ENV === 'development' && token === 'demo-token') {
            req.user = {
                _id: 'demo-user',
                username: 'demo',
                email: '<EMAIL>',
                role: 'admin',
                permissions: ['*'], // All permissions for demo
                isActive: true,
                isDeleted: false
            };
            return next();
        }

        // Verify token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);

        // Find user
        const user = await User.findById(decoded.id);
        
        if (!user) {
            return res.status(401).json({
                success: false,
                message: 'Invalid token. User not found.'
            });
        }

        // Check if user is active
        if (!user.isActive) {
            return res.status(403).json({
                success: false,
                message: 'Account is deactivated.'
            });
        }

        // Check if user is deleted
        if (user.isDeleted) {
            return res.status(403).json({
                success: false,
                message: 'Account no longer exists.'
            });
        }

        // Check if password was changed after token was issued
        if (user.passwordChangedAt && decoded.iat < user.passwordChangedAt.getTime() / 1000) {
            return res.status(401).json({
                success: false,
                message: 'Password was changed. Please login again.'
            });
        }

        // Update last activity
        user.lastActivity = new Date();
        await user.save();

        // Attach user to request
        req.user = user;
        req.token = token;

        next();

    } catch (error) {
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({
                success: false,
                message: 'Invalid token.'
            });
        }
        
        if (error.name === 'TokenExpiredError') {
            return res.status(401).json({
                success: false,
                message: 'Token expired.'
            });
        }

        console.error('Auth middleware error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error.'
        });
    }
};

/**
 * Role-based authorization middleware
 * Checks if user has required role
 */
const authorize = (...roles) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required.'
            });
        }

        if (!roles.includes(req.user.role)) {
            return res.status(403).json({
                success: false,
                message: 'Insufficient permissions.'
            });
        }

        next();
    };
};

/**
 * Permission-based authorization middleware
 * Checks if user has required permission
 */
const requirePermission = (permission) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required.'
            });
        }

        // Super admin has all permissions
        if (req.user.role === 'super_admin') {
            return next();
        }

        // Demo mode - allow all permissions
        if (req.user.permissions && req.user.permissions.includes('*')) {
            return next();
        }

        if (!req.user.permissions || !req.user.permissions.includes(permission)) {
            return res.status(403).json({
                success: false,
                message: `Permission '${permission}' required.`
            });
        }

        next();
    };
};

/**
 * Optional authentication middleware
 * Attaches user if token is provided, but doesn't require it
 */
const optionalAuth = async (req, res, next) => {
    try {
        const authHeader = req.header('Authorization');
        
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return next(); // No token provided, continue without user
        }

        const token = authHeader.substring(7);
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await User.findById(decoded.id);
        
        if (user && user.isActive && !user.isDeleted) {
            req.user = user;
            req.token = token;
        }

        next();

    } catch (error) {
        // If token is invalid, continue without user
        next();
    }
};

/**
 * Admin-only middleware
 * Shorthand for requiring admin role
 */
const adminOnly = [auth, authorize('admin', 'super_admin')];

/**
 * Super admin only middleware
 * Shorthand for requiring super admin role
 */
const superAdminOnly = [auth, authorize('super_admin')];

/**
 * Rate limiting for authentication endpoints
 */
const authRateLimit = require('express-rate-limit')({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // Limit each IP to 5 requests per windowMs
    message: {
        success: false,
        message: 'Too many authentication attempts. Please try again later.'
    },
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests: true // Don't count successful requests
});

/**
 * Middleware to log user activity
 */
const logActivity = (action, resource) => {
    return async (req, res, next) => {
        if (req.user) {
            const resourceId = req.params.id || req.body.id || 'unknown';
            const ipAddress = req.ip || req.connection.remoteAddress;
            const userAgent = req.get('User-Agent');
            
            req.user.logActivity(action, resource, resourceId, ipAddress, userAgent);
            await req.user.save();
        }
        next();
    };
};

/**
 * Middleware to check if user owns resource or is admin
 */
const ownerOrAdmin = (resourceField = 'userId') => {
    return async (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required.'
            });
        }

        // Admin can access any resource
        if (req.user.role === 'admin' || req.user.role === 'super_admin') {
            return next();
        }

        // Check if user owns the resource
        const resourceId = req.params.id;
        if (!resourceId) {
            return res.status(400).json({
                success: false,
                message: 'Resource ID required.'
            });
        }

        // This would need to be customized based on the specific resource
        // For now, we'll just check if the user ID matches
        if (req.user._id.toString() === resourceId) {
            return next();
        }

        return res.status(403).json({
            success: false,
            message: 'Access denied. You can only access your own resources.'
        });
    };
};

module.exports = {
    auth,
    authorize,
    requirePermission,
    optionalAuth,
    adminOnly,
    superAdminOnly,
    authRateLimit,
    logActivity,
    ownerOrAdmin
};
