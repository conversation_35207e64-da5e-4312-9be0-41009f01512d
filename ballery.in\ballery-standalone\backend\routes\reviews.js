const express = require('express');
const { body, query, validationResult } = require('express-validator');
const Review = require('../models/Review');
const Service = require('../models/Service');
const Booking = require('../models/Booking');
const { auth, requirePermission } = require('../middleware/auth');
const catchAsync = require('../utils/catchAsync');

const router = express.Router();

/**
 * @route   POST /api/reviews
 * @desc    Submit a new review
 * @access  Public
 */
router.post('/', [
    body('customerName').notEmpty().trim().isLength({ min: 2, max: 100 }),
    body('customerEmail').isEmail().normalizeEmail(),
    body('customerPhone').optional().isMobilePhone(),
    body('serviceName').notEmpty().trim(),
    body('serviceType').isIn(['virtual', 'site-visit']),
    body('rating').isInt({ min: 1, max: 5 }),
    body('title').optional().trim().isLength({ max: 200 }),
    body('reviewText').notEmpty().trim().isLength({ min: 10, max: 2000 }),
    body('projectLocation').optional().trim().isLength({ max: 200 }),
    body('projectType').optional().trim().isLength({ max: 100 }),
    body('projectValue').optional().isNumeric({ min: 0 })
], catchAsync(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
    }

    const {
        customerName,
        customerEmail,
        customerPhone,
        serviceName,
        serviceType,
        serviceId,
        bookingId,
        rating,
        title,
        reviewText,
        projectLocation,
        projectType,
        projectValue,
        source = 'website'
    } = req.body;

    // Check if service exists
    let service = null;
    if (serviceId) {
        service = await Service.findById(serviceId);
        if (!service) {
            return res.status(404).json({
                success: false,
                message: 'Service not found'
            });
        }
    }

    // Check if booking exists
    let booking = null;
    if (bookingId) {
        booking = await Booking.findById(bookingId);
        if (!booking) {
            return res.status(404).json({
                success: false,
                message: 'Booking not found'
            });
        }
    }

    // Create new review
    const review = new Review({
        customerName,
        customerEmail,
        customerPhone,
        serviceName,
        serviceType,
        serviceId: service ? service._id : null,
        bookingId: booking ? booking._id : null,
        rating,
        title,
        reviewText,
        projectLocation,
        projectType,
        projectValue,
        source,
        isVerified: booking ? true : false, // Auto-verify if from booking
        verificationMethod: booking ? 'booking' : 'email'
    });

    await review.save();

    res.status(201).json({
        success: true,
        message: 'Review submitted successfully',
        data: { review }
    });
}));

/**
 * @route   GET /api/reviews
 * @desc    Get all reviews (admin only)
 * @access  Private (Admin)
 */
router.get('/', [
    auth,
    requirePermission('reviews.view')
], [
    query('page').optional().isInt({ min: 1 }).toInt(),
    query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
    query('status').optional().isIn(['pending', 'approved', 'rejected', 'hidden']),
    query('rating').optional().isInt({ min: 1, max: 5 }),
    query('serviceType').optional().isIn(['virtual', 'site-visit']),
    query('serviceName').optional().trim(),
    query('search').optional().trim(),
    query('sortBy').optional().isIn(['submittedAt', 'rating', 'customerName', 'serviceName']),
    query('sortOrder').optional().isIn(['asc', 'desc'])
], catchAsync(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
    }

    const {
        page = 1,
        limit = 20,
        status,
        rating,
        serviceType,
        serviceName,
        search,
        sortBy = 'submittedAt',
        sortOrder = 'desc'
    } = req.query;

    // Build filter
    const filter = { isDeleted: false };
    
    if (status) filter.status = status;
    if (rating) filter.rating = rating;
    if (serviceType) filter.serviceType = serviceType;
    if (serviceName) filter.serviceName = new RegExp(serviceName, 'i');
    
    if (search) {
        filter.$or = [
            { customerName: new RegExp(search, 'i') },
            { customerEmail: new RegExp(search, 'i') },
            { reviewText: new RegExp(search, 'i') },
            { title: new RegExp(search, 'i') },
            { serviceName: new RegExp(search, 'i') }
        ];
    }

    // Build sort
    const sort = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [reviews, total] = await Promise.all([
        Review.find(filter)
            .populate('serviceId', 'serviceName serviceType price')
            .populate('bookingId', 'bookingId customerName customerEmail')
            .sort(sort)
            .skip(skip)
            .limit(limit),
        Review.countDocuments(filter)
    ]);

    res.json({
        success: true,
        data: {
            reviews,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        }
    });
}));

/**
 * @route   GET /api/reviews/public
 * @desc    Get approved reviews for public display
 * @access  Public
 */
router.get('/public', [
    query('page').optional().isInt({ min: 1 }).toInt(),
    query('limit').optional().isInt({ min: 1, max: 50 }).toInt(),
    query('rating').optional().isInt({ min: 1, max: 5 }),
    query('serviceType').optional().isIn(['virtual', 'site-visit']),
    query('featured').optional().isBoolean()
], catchAsync(async (req, res) => {
    const {
        page = 1,
        limit = 10,
        rating,
        serviceType,
        featured
    } = req.query;

    // Build filter for public reviews
    const filter = { 
        status: 'approved',
        showOnWebsite: true,
        isDeleted: false 
    };
    
    if (rating) filter.rating = rating;
    if (serviceType) filter.serviceType = serviceType;
    if (featured === 'true') filter.isFeatured = true;

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [reviews, total] = await Promise.all([
        Review.find(filter)
            .select('customerName serviceName serviceType rating title reviewText projectLocation projectType submittedAt isFeatured')
            .sort({ isFeatured: -1, displayOrder: 1, submittedAt: -1 })
            .skip(skip)
            .limit(limit),
        Review.countDocuments(filter)
    ]);

    res.json({
        success: true,
        data: {
            reviews,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        }
    });
}));

/**
 * @route   GET /api/reviews/:id
 * @desc    Get review by ID
 * @access  Private (Admin)
 */
router.get('/:id', [
    auth,
    requirePermission('reviews.view')
], catchAsync(async (req, res) => {
    const review = await Review.findById(req.params.id)
        .populate('serviceId', 'serviceName serviceType price')
        .populate('bookingId', 'bookingId customerName customerEmail status');

    if (!review || review.isDeleted) {
        return res.status(404).json({
            success: false,
            message: 'Review not found'
        });
    }

    // Increment view count
    await review.incrementViews();

    res.json({
        success: true,
        data: { review }
    });
}));

/**
 * @route   PUT /api/reviews/:id/status
 * @desc    Update review status (approve/reject/hide)
 * @access  Private (Admin)
 */
router.put('/:id/status', [
    auth,
    requirePermission('reviews.moderate')
], [
    body('status').isIn(['approved', 'rejected', 'hidden']),
    body('moderationNotes').optional().trim().isLength({ max: 500 })
], catchAsync(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
    }

    const { status, moderationNotes } = req.body;
    const moderatedBy = req.user.username || req.user.email;

    const review = await Review.findById(req.params.id);
    if (!review || review.isDeleted) {
        return res.status(404).json({
            success: false,
            message: 'Review not found'
        });
    }

    // Update status using instance methods
    switch (status) {
        case 'approved':
            await review.approve(moderatedBy);
            break;
        case 'rejected':
            await review.reject(moderatedBy, moderationNotes);
            break;
        case 'hidden':
            await review.hide(moderatedBy, moderationNotes);
            break;
    }

    res.json({
        success: true,
        message: `Review ${status} successfully`,
        data: { review }
    });
}));

/**
 * @route   PUT /api/reviews/bulk/status
 * @desc    Bulk update review status
 * @access  Private (Admin)
 */
router.put('/bulk/status', [
    auth,
    requirePermission('reviews.moderate')
], [
    body('reviewIds').isArray({ min: 1 }),
    body('reviewIds.*').isMongoId(),
    body('status').isIn(['approved', 'rejected', 'hidden']),
    body('moderationNotes').optional().trim().isLength({ max: 500 })
], catchAsync(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
    }

    const { reviewIds, status, moderationNotes } = req.body;
    const moderatedBy = req.user.username || req.user.email;

    const reviews = await Review.find({
        _id: { $in: reviewIds },
        isDeleted: false
    });

    if (reviews.length === 0) {
        return res.status(404).json({
            success: false,
            message: 'No reviews found'
        });
    }

    // Update all reviews
    const updatePromises = reviews.map(review => {
        switch (status) {
            case 'approved':
                return review.approve(moderatedBy);
            case 'rejected':
                return review.reject(moderatedBy, moderationNotes);
            case 'hidden':
                return review.hide(moderatedBy, moderationNotes);
        }
    });

    await Promise.all(updatePromises);

    res.json({
        success: true,
        message: `${reviews.length} reviews ${status} successfully`,
        data: { updatedCount: reviews.length }
    });
}));

/**
 * @route   PUT /api/reviews/:id/featured
 * @desc    Toggle featured status
 * @access  Private (Admin)
 */
router.put('/:id/featured', [
    auth,
    requirePermission('reviews.edit')
], catchAsync(async (req, res) => {
    const review = await Review.findById(req.params.id);
    if (!review || review.isDeleted) {
        return res.status(404).json({
            success: false,
            message: 'Review not found'
        });
    }

    await review.toggleFeatured();

    res.json({
        success: true,
        message: `Review ${review.isFeatured ? 'featured' : 'unfeatured'} successfully`,
        data: { review }
    });
}));

/**
 * @route   POST /api/reviews/:id/response
 * @desc    Add business response to review
 * @access  Private (Admin)
 */
router.post('/:id/response', [
    auth,
    requirePermission('reviews.respond')
], [
    body('responseText').notEmpty().trim().isLength({ min: 10, max: 1000 })
], catchAsync(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
    }

    const { responseText } = req.body;
    const respondedBy = req.user.username || req.user.email;

    const review = await Review.findById(req.params.id);
    if (!review || review.isDeleted) {
        return res.status(404).json({
            success: false,
            message: 'Review not found'
        });
    }

    await review.addBusinessResponse(responseText, respondedBy);

    res.json({
        success: true,
        message: 'Business response added successfully',
        data: { review }
    });
}));

/**
 * @route   DELETE /api/reviews/:id
 * @desc    Soft delete review
 * @access  Private (Admin)
 */
router.delete('/:id', [
    auth,
    requirePermission('reviews.delete')
], catchAsync(async (req, res) => {
    const review = await Review.findById(req.params.id);
    if (!review || review.isDeleted) {
        return res.status(404).json({
            success: false,
            message: 'Review not found'
        });
    }

    review.isDeleted = true;
    review.deletedAt = new Date();
    review.deletedBy = req.user.username || req.user.email;
    await review.save();

    res.json({
        success: true,
        message: 'Review deleted successfully'
    });
}));

/**
 * @route   GET /api/reviews/stats/summary
 * @desc    Get review statistics
 * @access  Private (Admin)
 */
router.get('/stats/summary', [
    auth,
    requirePermission('reviews.view')
], catchAsync(async (req, res) => {
    const [statusStats, ratingStats, serviceStats] = await Promise.all([
        Review.getReviewStats(),
        Review.getRatingDistribution(),
        Review.getServiceReviewStats()
    ]);

    const summary = {
        total: 0,
        pending: 0,
        approved: 0,
        rejected: 0,
        hidden: 0,
        averageRating: 0
    };

    let totalRating = 0;
    let ratingCount = 0;

    statusStats.forEach(stat => {
        summary.total += stat.count;
        summary[stat._id] = stat.count;
        if (stat.avgRating) {
            totalRating += stat.avgRating * stat.count;
            ratingCount += stat.count;
        }
    });

    summary.averageRating = ratingCount > 0 ? (totalRating / ratingCount).toFixed(1) : 0;

    res.json({
        success: true,
        data: {
            summary,
            ratingDistribution: ratingStats,
            serviceStats: serviceStats.slice(0, 10) // Top 10 services
        }
    });
}));

module.exports = router;
