/**
 * BUILDER BALLERY - Inquiry Routes
 * Handle customer inquiries and contact form submissions
 */

const express = require('express');
const { body, validationResult, query } = require('express-validator');
const Inquiry = require('../models/Inquiry');
const { auth, requirePermission } = require('../middleware/auth');
const { catchAsync, AppError } = require('../middleware/errorHandler');
const emailService = require('../services/emailService');

const router = express.Router();

/**
 * @route   POST /api/inquiries
 * @desc    Submit new inquiry/contact form
 * @access  Public
 */
router.post('/', [
    body('name')
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('Name must be between 2 and 100 characters'),
    body('email')
        .optional()
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    body('phone')
        .optional()
        .matches(/^[+]?[\d\s\-\(\)]{10,15}$/)
        .withMessage('Please provide a valid phone number'),
    body('subject')
        .trim()
        .isLength({ min: 5, max: 200 })
        .withMessage('Subject must be between 5 and 200 characters'),
    body('message')
        .trim()
        .isLength({ min: 10, max: 1000 })
        .withMessage('Message must be between 10 and 1000 characters'),
    body('inquiryType')
        .optional()
        .isIn(['general', 'booking', 'pricing', 'technical', 'complaint'])
        .withMessage('Invalid inquiry type')
], catchAsync(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
    }

    const {
        name,
        email,
        phone,
        subject,
        message,
        inquiryType = 'general'
    } = req.body;

    const inquiry = new Inquiry({
        name,
        email,
        phone,
        subject,
        message,
        inquiryType,
        source: 'website_contact',
        ipAddress: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent')
    });

    await inquiry.save();

    // Send welcome email
    try {
        await emailService.sendWelcomeEmail(inquiry);
        console.log(`✅ Welcome email sent to ${inquiry.email}`);
    } catch (emailError) {
        console.error('❌ Failed to send welcome email:', emailError);
        // Don't fail the request if email fails
    }

    res.status(201).json({
        success: true,
        message: 'Inquiry submitted successfully. We will get back to you soon!',
        data: {
            inquiryId: inquiry.inquiryId,
            name: inquiry.name,
            email: inquiry.email,
            subject: inquiry.subject,
            submittedAt: inquiry.submittedAt
        }
    });
}));

/**
 * @route   POST /api/inquiries/callback
 * @desc    Submit callback request
 * @access  Public
 */
router.post('/callback', [
    body('name')
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('Name must be between 2 and 100 characters'),
    body('phone')
        .matches(/^[+]?[\d\s\-\(\)]{10,15}$/)
        .withMessage('Please provide a valid phone number'),
    body('message')
        .optional()
        .trim()
        .isLength({ max: 500 })
        .withMessage('Message must be less than 500 characters')
], catchAsync(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
    }

    const { name, phone, message } = req.body;

    const inquiry = new Inquiry({
        name,
        phone,
        subject: 'Callback Request',
        message: message || 'Customer requested a callback',
        inquiryType: 'general',
        source: 'website_callback',
        ipAddress: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent')
    });

    await inquiry.save();

    res.status(201).json({
        success: true,
        message: 'Callback request submitted successfully. We will call you back soon!',
        data: {
            inquiryId: inquiry.inquiryId,
            name: inquiry.name,
            phone: inquiry.phone,
            submittedAt: inquiry.submittedAt
        }
    });
}));

/**
 * @route   GET /api/inquiries
 * @desc    Get all inquiries (admin only)
 * @access  Private (Admin)
 */
router.get('/', [
    auth,
    requirePermission('inquiries.view')
], [
    query('page').optional().isInt({ min: 1 }).toInt(),
    query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
    query('status').optional().isIn(['unread', 'read', 'responded', 'resolved']),
    query('inquiryType').optional().isIn(['general', 'booking', 'pricing', 'technical', 'complaint']),
    query('sortBy').optional().isIn(['submittedAt', 'name', 'subject', 'status']),
    query('sortOrder').optional().isIn(['asc', 'desc'])
], catchAsync(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
    }

    const {
        page = 1,
        limit = 20,
        status,
        inquiryType,
        sortBy = 'submittedAt',
        sortOrder = 'desc',
        search
    } = req.query;

    // Build filter
    const filter = {};
    
    if (status) filter.status = status;
    if (inquiryType) filter.inquiryType = inquiryType;
    
    if (search) {
        filter.$or = [
            { name: { $regex: search, $options: 'i' } },
            { email: { $regex: search, $options: 'i' } },
            { subject: { $regex: search, $options: 'i' } },
            { message: { $regex: search, $options: 'i' } }
        ];
    }

    // Build sort
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query
    const skip = (page - 1) * limit;
    
    const [inquiries, total] = await Promise.all([
        Inquiry.find(filter)
            .sort(sort)
            .skip(skip)
            .limit(limit)
            .lean(),
        Inquiry.countDocuments(filter)
    ]);

    const totalPages = Math.ceil(total / limit);

    res.json({
        success: true,
        data: {
            inquiries,
            pagination: {
                currentPage: page,
                totalPages,
                totalItems: total,
                itemsPerPage: limit,
                hasNextPage: page < totalPages,
                hasPrevPage: page > 1
            }
        }
    });
}));

/**
 * @route   PUT /api/inquiries/:id/status
 * @desc    Update inquiry status
 * @access  Private (Admin)
 */
router.put('/:id/status', [
    auth,
    requirePermission('inquiries.respond')
], [
    body('status')
        .isIn(['unread', 'read', 'responded', 'resolved'])
        .withMessage('Invalid status'),
    body('response').optional().trim().isLength({ max: 1000 }),
    body('notes').optional().trim().isLength({ max: 500 })
], catchAsync(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
    }

    const { id } = req.params;
    const { status, response, notes } = req.body;

    const inquiry = await Inquiry.findById(id);
    
    if (!inquiry) {
        throw new AppError('Inquiry not found', 404);
    }

    // Update inquiry
    inquiry.status = status;
    if (response) inquiry.adminResponse = response;
    if (notes) inquiry.adminNotes = notes;
    inquiry.respondedAt = new Date();
    inquiry.respondedBy = req.user._id;

    await inquiry.save();

    // Send email response if response is provided
    if (response && inquiry.email) {
        try {
            await emailService.sendInquiryResponse(inquiry, response);
            console.log(`✅ Response email sent to ${inquiry.email}`);
        } catch (emailError) {
            console.error('❌ Failed to send response email:', emailError);
            // Don't fail the request if email fails
        }
    }

    res.json({
        success: true,
        message: 'Inquiry status updated successfully',
        data: {
            inquiry: {
                id: inquiry._id,
                inquiryId: inquiry.inquiryId,
                status: inquiry.status,
                adminResponse: inquiry.adminResponse,
                adminNotes: inquiry.adminNotes,
                respondedAt: inquiry.respondedAt
            }
        }
    });
}));

/**
 * @route   GET /api/inquiries/stats
 * @desc    Get inquiry statistics
 * @access  Private (Admin)
 */
router.get('/stats', [
    auth,
    requirePermission('inquiries.view')
], catchAsync(async (req, res) => {
    const stats = await Inquiry.aggregate([
        {
            $group: {
                _id: '$status',
                count: { $sum: 1 }
            }
        }
    ]);

    const summary = {
        total: 0,
        unread: 0,
        read: 0,
        responded: 0,
        resolved: 0
    };

    stats.forEach(stat => {
        summary.total += stat.count;
        summary[stat._id] = stat.count;
    });

    res.json({
        success: true,
        data: { summary, details: stats }
    });
}));

/**
 * @route   POST /api/inquiries/bulk/status
 * @desc    Update multiple inquiries status
 * @access  Private (Admin)
 */
router.post('/bulk/status', [
    auth,
    requirePermission('inquiries.respond')
], [
    body('inquiryIds')
        .isArray({ min: 1 })
        .withMessage('At least one inquiry ID is required'),
    body('status')
        .isIn(['unread', 'read', 'responded', 'resolved'])
        .withMessage('Invalid status'),
    body('notes').optional().trim().isLength({ max: 500 })
], catchAsync(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
    }

    const { inquiryIds, status, notes } = req.body;

    const updateData = {
        status,
        respondedAt: new Date(),
        respondedBy: req.user._id
    };

    if (notes) updateData.adminNotes = notes;

    const result = await Inquiry.updateMany(
        { _id: { $in: inquiryIds }, isDeleted: false },
        updateData
    );

    res.json({
        success: true,
        message: `${result.modifiedCount} inquiries updated successfully`,
        data: {
            modifiedCount: result.modifiedCount,
            matchedCount: result.matchedCount
        }
    });
}));

/**
 * @route   POST /api/inquiries/bulk/email
 * @desc    Send bulk email to multiple inquiries
 * @access  Private (Admin)
 */
router.post('/bulk/email', [
    auth,
    requirePermission('inquiries.respond')
], [
    body('inquiryIds')
        .isArray({ min: 1 })
        .withMessage('At least one inquiry ID is required'),
    body('subject')
        .trim()
        .isLength({ min: 5, max: 200 })
        .withMessage('Subject must be between 5 and 200 characters'),
    body('message')
        .trim()
        .isLength({ min: 10, max: 2000 })
        .withMessage('Message must be between 10 and 2000 characters')
], catchAsync(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
    }

    const { inquiryIds, subject, message } = req.body;

    // Get inquiries
    const inquiries = await Inquiry.find({
        _id: { $in: inquiryIds },
        isDeleted: false
    }).select('name email inquiryId');

    if (inquiries.length === 0) {
        return res.status(404).json({
            success: false,
            message: 'No inquiries found'
        });
    }

    // Send bulk emails
    const emailResults = await emailService.sendBulkEmail(
        inquiries.map(inq => ({ email: inq.email, name: inq.name })),
        subject,
        message
    );

    // Update inquiries status
    await Inquiry.updateMany(
        { _id: { $in: inquiryIds } },
        {
            status: 'responded',
            adminResponse: message,
            respondedAt: new Date(),
            respondedBy: req.user._id
        }
    );

    const successCount = emailResults.filter(r => r.success).length;
    const failureCount = emailResults.filter(r => !r.success).length;

    res.json({
        success: true,
        message: `Bulk email sent: ${successCount} successful, ${failureCount} failed`,
        data: {
            totalSent: successCount,
            totalFailed: failureCount,
            results: emailResults
        }
    });
}));

/**
 * @route   DELETE /api/inquiries/bulk
 * @desc    Bulk delete inquiries
 * @access  Private (Admin)
 */
router.delete('/bulk', [
    auth,
    requirePermission('inquiries.delete')
], [
    body('inquiryIds')
        .isArray({ min: 1 })
        .withMessage('At least one inquiry ID is required')
], catchAsync(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
    }

    const { inquiryIds } = req.body;

    // Soft delete
    const result = await Inquiry.updateMany(
        { _id: { $in: inquiryIds } },
        {
            isDeleted: true,
            deletedAt: new Date(),
            deletedBy: req.user._id
        }
    );

    res.json({
        success: true,
        message: `${result.modifiedCount} inquiries deleted successfully`,
        data: {
            deletedCount: result.modifiedCount
        }
    });
}));

module.exports = router;
